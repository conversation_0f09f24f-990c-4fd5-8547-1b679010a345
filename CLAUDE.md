# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

光流一站式口播助手 (Guangliu All-in-One Voice Broadcasting Assistant) is a comprehensive Python-based desktop application for digital human video generation and audio processing. The project integrates multiple services:

- **Fish Audio API**: Voice cloning and text-to-speech
- **Hifly Digital Human API**: Video generation with lip-sync
- **AI Watermark Removal**: Video post-processing capabilities

## Architecture

### Core Structure
```
src/
├── main.py                    # Main application entry point (PySide6 GUI)
├── core/                      # Business logic modules
│   ├── processor.py           # Audio processing engine
│   ├── digital_human_manager.py # Digital human automation
│   ├── video_processor.py     # Video processing capabilities
│   ├── hifly_integration.py   # Hifly API integration
│   ├── clipboard_manager.py   # Clipboard monitoring
│   ├── config_manager.py      # Configuration management
│   ├── text_converter.py      # Text processing utilities
│   └── auth_updater.py        # Authentication management
├── ui/                        # User interface components
│   ├── main_window.py         # Main GUI window (PySide6)
│   ├── settings_dialog.py     # Settings configuration
│   ├── schedule_manager.py    # Task scheduling interface
│   ├── styles.qss             # Qt stylesheet
│   └── icons/                 # UI icons and assets
└── watermark_removal/         # AI watermark removal module
    ├── ai_watermark_remover.py
    ├── ffmpeg_ai_watermark_remover.py
    └── region_scaler.py
```

### Key Integrations
- **hifly/**: Standalone Hifly API client module
- **feiyingshuziren/**: Digital human automation scripts with batch processing
- **config/**: JSON configuration files for application settings
- **data/**: Excel templates and voice ID mappings

## Common Commands

### Development
```bash
# Install dependencies
pip install -r requirements.txt

# Run the application (GUI)
python src/main.py
# OR use the batch script
start.bat

# Run Hifly standalone module
cd hifly
python quick_start.py
python hifly_runner.py

# Test Hifly integration
python test_hifly_api.py
python verify_hifly_fix.py
```

### Testing
```bash
# Run various test files
python test_integration.py
python test_quick_mode.py
python build_test.py
python check_hifly_credits.py
```

### Environment Setup
```bash
# Set Python path (handled by start.bat)
set PYTHONPATH=%cd%\src;%PYTHONPATH%

# For development, ensure UTF-8 encoding
chcp 65001
```

## Development Guidelines

### Module Import Pattern
- All imports use absolute paths from `src/` directory
- Main entry point handles path setup for both development and packaged environments
- UI components import from `ui.` namespace
- Core business logic imports from `core.` namespace

### Configuration Management
- Settings stored in `config/settings.json`
- Application uses `ConfigManager` class for centralized config access
- Supports both local Excel files and online Baidu spreadsheet integration
- API tokens and credentials managed through settings dialog

### Data Processing Flow
1. **Input**: Excel templates or clipboard data monitoring
2. **Voice Processing**: Fish Audio API for voice cloning
3. **Digital Human**: Hifly API for video generation with lip-sync
4. **Post-processing**: AI watermark removal using IOPaint + FFmpeg
5. **Output**: Organized by date and processing mode (快速/暗黑/积分)

### GUI Framework
- Built with PySide6 (Qt6)
- Custom dark/light theme support via `styles.qss`
- Async processing with progress tracking
- System tray integration and notification support

### Packaging
- Uses PyInstaller with `build_exe.spec` configuration
- Handles resource file paths for both development and packaged environments
- Icon and asset management for standalone executables

## API Integration Details

### Fish Audio API
- Voice cloning and text-to-speech services
- Batch processing with concurrent request handling
- Progress tracking and status management
- Audio file format conversion and optimization

### Hifly Digital Human API
- Video generation with reference video/image + audio
- Automatic upload, processing, and download workflow
- Support for both file paths and URLs
- Task status monitoring and retry mechanisms

### Baidu Spreadsheet Integration
- API mode and web scraping fallback
- Real-time data synchronization
- Pagination support for large datasets
- Local caching with refresh capabilities

## File Organization

### Output Structure
```
output/
├── 生成结果_YYYYMMDD/
│   ├── 文案_YYYYMMDD.xlsx
│   └── 配音文件_YYYYMMDD_HHMMSS/
└── 创作任务_YYYYMMDD/
    ├── 已完成/
    │   ├── 快速/    # Quick processing mode
    │   ├── 暗黑/    # Dark mode processing
    │   └── 积分/    # Points mode processing
    └── 生成结果记录.xlsx
```

### Configuration Files
- `config/settings.json`: User preferences and API settings
- `config/text_extractor_rules.json`: Text processing rules
- `data/voice_id_list.xlsx`: Voice ID mappings
- `data/script_template.xlsx`: Content templates

## Dependencies

### Core Dependencies
- `PySide6>=6.4.0`: GUI framework
- `pandas>=1.3.0`: Data manipulation
- `httpx>=0.23.0`: HTTP client for API calls
- `tqdm>=4.64.0`: Progress bars
- `python-dotenv>=0.20.0`: Environment variables
- `pypinyin>=0.47.0`: Chinese text processing
- `pyinstaller>=5.7.0`: Application packaging

### AI Processing
- `iopaint`: AI-based watermark removal
- `pillow`: Image processing
- `numpy`: Numerical computations
- `scipy`: Advanced image processing

### System Requirements
- Python 3.8+
- Windows 10/11 (primary target)
- FFmpeg (for video processing)

## Troubleshooting

### Common Issues
1. **Path Resolution**: Application handles both development and packaged environments
2. **Resource Loading**: Icons and styles loaded with fallback paths
3. **API Integration**: Built-in retry mechanisms and error handling
4. **File Encoding**: UTF-8 handling for Chinese content

### Debug Mode
- Console output provides detailed environment and dependency information
- Error dialogs show full stack traces
- Log files generated in date-organized directories

## Integration Notes

This is a production application with active daily usage for digital content creation. The codebase includes extensive error handling, progress tracking, and user-friendly interfaces for batch processing workflows.