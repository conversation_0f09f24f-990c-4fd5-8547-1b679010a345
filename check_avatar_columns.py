#!/usr/bin/env python3
"""
检查avatar_list.xlsx文件的列名
"""

import pandas as pd
import os

def check_avatar_columns():
    """检查avatar_list.xlsx文件的列名"""
    try:
        file_path = "data/avatar_list.xlsx"
        
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return
        
        print(f"📁 检查文件: {file_path}")
        
        # 读取文件
        df = pd.read_excel(file_path)
        
        print(f"📊 文件包含 {len(df)} 条记录")
        print(f"📋 文件包含 {len(df.columns)} 列")
        
        print("\n列名列表:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i:2d}. {col}")
        
        # 检查是否有日期相关的列
        date_related_columns = []
        for col in df.columns:
            if any(keyword in col.lower() for keyword in ['日期', 'date', '时间', 'time', '更新', 'update']):
                date_related_columns.append(col)
        
        if date_related_columns:
            print(f"\n📅 找到日期相关列: {date_related_columns}")
            
            # 显示这些列的示例数据
            for col in date_related_columns:
                print(f"\n列 '{col}' 的前5个值:")
                sample_values = df[col].head().tolist()
                for i, value in enumerate(sample_values, 1):
                    print(f"  {i}. {value}")
        else:
            print("\n❌ 未找到日期相关列")
        
        # 检查所有列的数据类型
        print(f"\n📋 列数据类型:")
        for col in df.columns:
            dtype = df[col].dtype
            non_null_count = df[col].count()
            null_count = len(df) - non_null_count
            print(f"  {col}: {dtype} (非空: {non_null_count}, 空值: {null_count})")
        
        return df.columns.tolist()
        
    except Exception as e:
        print(f"❌ 检查文件失败: {e}")
        return None

if __name__ == "__main__":
    columns = check_avatar_columns()
    
    if columns:
        print(f"\n✅ 文件检查完成，共 {len(columns)} 列")
    else:
        print("\n❌ 文件检查失败")
