#!/usr/bin/env python3
"""
最终路径修复验证测试
"""

import sys
import os
import pandas as pd

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_final_path_fix():
    """最终路径修复验证"""
    try:
        from core.voice_manager import VoiceManager
        
        print("✓ 模块导入成功")
        
        # 创建实例
        vm = VoiceManager()
        
        # 验证所有路径配置
        print("✓ 验证路径配置:")
        print(f"  - 项目根目录: {vm.project_root}")
        print(f"  - 声音数据目录: {vm.VOICE_DATA_DIR}")
        print(f"  - JSON文件: {vm.VOICE_DATA_FILE}")
        print(f"  - Excel文件: {vm.EXCEL_FILE}")
        print(f"  - 音频上传目录: {vm.AUDIO_UPLOAD_DIR}")
        print(f"  - 已上传目录: {vm.AUDIO_UPLOADED_DIR}")
        
        # 验证Excel文件路径是否正确
        expected_excel = os.path.join(vm.VOICE_DATA_DIR, "声音ID列表.xlsx")
        if vm.EXCEL_FILE == expected_excel:
            print("  ✓ Excel文件路径配置正确")
        else:
            print(f"  ❌ Excel文件路径配置错误")
            return False
        
        # 验证目录结构
        print("\n✓ 验证目录结构:")
        if os.path.exists(vm.VOICE_DATA_DIR):
            print(f"  ✓ 声音数据目录存在: {vm.VOICE_DATA_DIR}")
            
            # 列出目录内容
            items = os.listdir(vm.VOICE_DATA_DIR)
            for item in items:
                item_path = os.path.join(vm.VOICE_DATA_DIR, item)
                if os.path.isfile(item_path):
                    size = os.path.getsize(item_path)
                    print(f"    📄 {item} ({size} bytes)")
                elif os.path.isdir(item_path):
                    count = len(os.listdir(item_path))
                    print(f"    📁 {item}/ ({count} items)")
        else:
            print(f"  ⚠️ 声音数据目录不存在: {vm.VOICE_DATA_DIR}")
        
        # 测试Excel文件操作
        print("\n✓ 测试Excel文件操作:")
        
        # 创建测试数据
        test_data = [
            {
                'name': '最终测试声音1',
                'modelId': 'final_test_1',
                'url': 'https://fish.audio/model/final_test_1',
                'extractTime': '2024-12-01 19:00:00'
            },
            {
                'name': '最终测试声音2',
                'modelId': 'final_test_2',
                'url': 'https://fish.audio/model/final_test_2',
                'extractTime': '2024-12-01 20:00:00'
            }
        ]
        
        # 保存测试数据
        vm.voice_models = test_data
        save_success = vm._save_to_excel()
        
        if save_success:
            print("  ✓ Excel文件保存成功")
            
            # 验证文件存在
            if os.path.exists(vm.EXCEL_FILE):
                print(f"  ✓ Excel文件存在: {vm.EXCEL_FILE}")
                
                # 读取并验证
                df = pd.read_excel(vm.EXCEL_FILE)
                print(f"  ✓ 读取成功，包含 {len(df)} 行数据")
                
                # 测试名称读取
                existing_names = vm._load_existing_names_from_excel()
                print(f"  ✓ 名称读取成功，共 {len(existing_names)} 个名称")
                
            else:
                print(f"  ❌ Excel文件不存在: {vm.EXCEL_FILE}")
                return False
        else:
            print("  ❌ Excel文件保存失败")
            return False
        
        # 验证路径一致性
        print("\n✓ 验证路径一致性:")
        
        # 检查是否有旧位置的文件
        old_excel_path = os.path.join(vm.project_root, "声音ID列表.xlsx")
        if os.path.exists(old_excel_path):
            print(f"  ⚠️ 发现旧位置的Excel文件: {old_excel_path}")
            print("  建议删除旧文件以避免混淆")
        else:
            print("  ✓ 未发现旧位置的Excel文件")
        
        # 验证新位置
        if os.path.exists(vm.EXCEL_FILE):
            print(f"  ✓ 新位置Excel文件正常: {vm.EXCEL_FILE}")
        else:
            print(f"  ❌ 新位置Excel文件不存在: {vm.EXCEL_FILE}")
            return False
        
        print("\n🎉 最终路径修复验证通过！")
        return True
        
    except Exception as e:
        print(f"❌ 最终验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚀 开始最终路径修复验证...\n")
    
    success = test_final_path_fix()
    
    print("\n" + "="*60 + "\n")
    
    if success:
        print("🎉 路径修复验证通过！Excel文件位置已正确。")
        print("\n📋 修复总结:")
        print("1. ✅ Excel文件路径已修正为 voice_management/声音ID列表.xlsx")
        print("2. ✅ 所有声音管理文件统一存放在 voice_management 目录")
        print("3. ✅ 文件读写功能正常")
        print("4. ✅ 目录结构更加合理和统一")
        print("\n📁 最终目录结构:")
        print("voice_management/")
        print("├── 声音ID列表.xlsx      # Excel格式数据")
        print("├── voice_models.json    # JSON格式数据")
        print("├── audio_files/         # 待上传音频")
        print("└── uploaded_files/      # 已上传音频")
        print("\n现在所有声音管理相关文件都在统一的目录中！")
    else:
        print("❌ 验证失败，请检查错误信息。")
        sys.exit(1)

if __name__ == "__main__":
    main()
