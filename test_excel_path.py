#!/usr/bin/env python3
"""
测试Excel文件路径修复
"""

import sys
import os
import pandas as pd

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_excel_path():
    """测试Excel文件路径"""
    try:
        from core.voice_manager import VoiceManager
        
        print("✓ 模块导入成功")
        
        # 创建实例
        vm = VoiceManager()
        
        # 检查路径配置
        print("✓ 检查路径配置:")
        print(f"  - 项目根目录: {vm.project_root}")
        print(f"  - 声音数据目录: {vm.VOICE_DATA_DIR}")
        print(f"  - Excel文件路径: {vm.EXCEL_FILE}")
        
        # 验证路径是否正确
        expected_path = os.path.join(vm.project_root, "voice_management", "声音ID列表.xlsx")
        if vm.EXCEL_FILE == expected_path:
            print("  ✓ Excel文件路径配置正确")
        else:
            print(f"  ❌ Excel文件路径配置错误")
            print(f"    期望: {expected_path}")
            print(f"    实际: {vm.EXCEL_FILE}")
            return False
        
        # 测试目录创建
        print("✓ 测试目录创建:")
        if os.path.exists(vm.VOICE_DATA_DIR):
            print(f"  ✓ 声音数据目录已存在: {vm.VOICE_DATA_DIR}")
        else:
            print(f"  ⚠️ 声音数据目录不存在，将自动创建")
        
        # 测试Excel文件保存
        print("✓ 测试Excel文件保存:")
        
        # 添加测试数据
        vm.voice_models = [
            {
                'name': '路径测试声音1',
                'modelId': 'path_test_1',
                'url': 'https://fish.audio/model/path_test_1',
                'extractTime': '2024-12-01 17:00:00'
            },
            {
                'name': '路径测试声音2',
                'modelId': 'path_test_2',
                'url': 'https://fish.audio/model/path_test_2',
                'extractTime': '2024-12-01 18:00:00'
            }
        ]
        
        # 保存到Excel
        save_success = vm._save_to_excel()
        if save_success:
            print("  ✓ Excel文件保存成功")
            
            # 验证文件是否在正确位置
            if os.path.exists(vm.EXCEL_FILE):
                print(f"  ✓ Excel文件已创建: {vm.EXCEL_FILE}")
                
                # 读取并验证内容
                df = pd.read_excel(vm.EXCEL_FILE)
                print(f"  ✓ Excel文件包含 {len(df)} 行数据")
                print(f"  ✓ 列名: {list(df.columns)}")
                
                # 显示文件内容
                print("\n📋 Excel文件内容:")
                print(df.to_string(index=False))
                
            else:
                print(f"  ❌ Excel文件未在预期位置创建")
                return False
        else:
            print("  ❌ Excel文件保存失败")
            return False
        
        # 测试读取功能
        print("\n✓ 测试Excel文件读取:")
        existing_names = vm._load_existing_names_from_excel()
        print(f"  ✓ 从Excel读取到 {len(existing_names)} 个声音名称")
        print(f"  ✓ 名称列表: {list(existing_names)}")
        
        print("\n🎉 Excel文件路径修复测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ Excel路径测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚀 开始测试Excel文件路径修复...\n")
    
    success = test_excel_path()
    
    print("\n" + "="*50 + "\n")
    
    if success:
        print("🎉 测试通过！Excel文件路径已修复。")
        print("\n📋 修复内容:")
        print("1. ✅ Excel文件路径已修正")
        print("2. ✅ 文件保存到 voice_management/声音ID列表.xlsx")
        print("3. ✅ 文件读取功能正常")
        print("4. ✅ 目录结构更加合理")
        print("\n📁 新的文件结构:")
        print("voice_management/")
        print("├── 声音ID列表.xlsx")
        print("├── voice_models.json")
        print("├── audio_files/")
        print("└── uploaded_files/")
    else:
        print("❌ 测试失败，请检查错误信息。")
        sys.exit(1)

if __name__ == "__main__":
    main()
