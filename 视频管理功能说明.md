# 视频管理功能说明

## 功能概述

视频管理模块提供完整的视频素材管理功能，支持从网站自动下载素材表格、数据处理、增量更新和飞影平台上传等功能。

## 主要功能

### 1. 素材更新 ✅
- **功能**: 从指定网站自动下载最新的素材表格数据
- **特点**:
  - 自动访问素材管理网站
  - 智能设置查询条件（最近一年、拍摄素材库）
  - 自动下载表格文件
  - 数据去重处理（根据素材ID）
  - 列名自动映射
  - 增量更新到本地文件
- **操作**: 点击"素材更新"按钮，确认后自动执行
- **状态**: 已完成并测试通过

### 2. 数据显示 ✅
- **功能**: 显示最近一周更新的素材信息
- **显示列**:
  - 序号
  - ID（素材ID）
  - 视频URL（外链BOS地址）
  - 上传人邮箱后缀
  - 拍摄演员名称
  - 视频版型
  - 场景
  - 表现形式
  - 服装
  - 是否上传飞影
  - 更新日期
- **特点**: 只显示最近7天的数据，自动过滤历史数据

### 3. 搜索功能 ✅
- **功能**: 实时搜索素材信息，支持高亮显示和结果导航
- **特点**:
  - 实时搜索，输入2个字符开始过滤
  - 搜索结果高亮显示（黄色背景）
  - 支持上一个/下一个结果导航
  - 循环导航（到达末尾回到开头）
  - 显示当前搜索位置 (x/y)
  - 自动滚动到当前匹配项
- **操作**: 在搜索框输入关键词，使用导航按钮浏览结果

### 4. 素材位置 ✅
- **功能**: 快速打开素材文件夹
- **特点**: 
  - 一键打开data目录
  - 跨平台支持（Windows/macOS/Linux）
  - 方便查看和管理本地文件
- **操作**: 点击"素材位置"按钮

### 5. 飞影上传 🔄
- **功能**: 批量上传标记为未上传的素材到飞影平台
- **特点**:
  - 筛选"是否上传飞影"为空的记录
  - 批量API上传
  - 实时进度显示
  - 自动更新表格状态
- **操作**: 点击"飞影上传"按钮
- **状态**: 待实现

## 数据处理逻辑

### 1. 下载和去重
```
网站下载 → 读取Excel → 根据素材ID去重 → 保留唯一记录
```

### 2. 列名映射
```
素材ID → ID
外链BOS地址 → 视频URL
其他列 → 保持原名
```

### 3. 增量更新
```
读取现有数据 → 对比ID列 → 只添加新记录 → 插入到表格顶部
```

### 4. 时间过滤
```
读取所有数据 → 过滤更新日期 → 只显示最近7天 → 表格展示
```

## 使用流程

### 1. 首次使用
1. 确保安装了playwright依赖：
   ```bash
   pip install playwright
   playwright install chromium
   ```
2. 启动程序，点击左侧"视频管理"图标
3. 查看现有数据（如果有的话）

### 2. 素材更新
1. 点击"素材更新"按钮
2. 确认更新对话框
3. 等待自动下载和处理（可能需要几分钟）
4. 查看更新结果和新增记录数量

### 3. 数据查看
1. 表格自动显示最近一周的数据
2. 使用搜索框查找特定素材
3. 点击列标题进行排序
4. 查看详细的素材信息

### 4. 文件管理
1. 点击"素材位置"打开data文件夹
2. 查看avater_list.xlsx文件
3. 检查temp文件夹的临时下载文件

## 界面说明

### 工具栏
- **搜索框**: 实时搜索素材，支持多列搜索
- **清除按钮**: 清除搜索内容和高亮
- **上一个按钮**: 导航到上一个搜索结果
- **下一个按钮**: 导航到下一个搜索结果
- **素材更新**: 从网站下载最新素材数据
- **素材位置**: 打开素材文件夹

### 表格区域
- **外边框**: 美观的圆角边框设计
- **列标题**: 11列关键信息
- **排序功能**: 点击列标题可排序
- **选择功能**: 支持单选和多选
- **搜索高亮**: 匹配的搜索结果会高亮显示

### 底部控制区
- **飞影上传**: 上传未上传的素材到飞影平台
- **进度条**: 显示上传进度和状态

### 日志区域
- **边框样式**: 与其他模块一致的边框设计
- **展开/收起**: 可控制日志区域显示
- **等宽字体**: 便于阅读日志信息
- **自动滚动**: 新日志自动滚动到底部

## 文件结构

```
data/
├── avater_list.xlsx          # 主要素材列表文件
├── temp/                     # 临时下载文件夹
│   └── material_data_*.xlsx  # 下载的临时文件
└── 其他数据文件...
```

## 技术实现

### 核心类
- **VideoMaterialManager**: 视频素材管理器
- **MaterialUpdateWorker**: 素材更新工作线程

### 关键方法
- `download_material_data()`: 自动下载素材数据
- `process_downloaded_data()`: 处理下载的数据
- `merge_with_existing_data()`: 与现有数据合并
- `get_recent_week_data()`: 获取最近一周数据
- `get_display_columns()`: 获取显示列

### 网站自动化
使用Playwright进行网站自动化操作：
1. 访问素材管理页面
2. 设置素材创建日期为"最近一年"
3. 选择"拍摄素材库"
4. 点击导出按钮
5. 等待下载完成

## 注意事项

### 1. 依赖要求
- **pandas**: 数据处理
- **playwright**: 网站自动化（可选）
- **PySide6**: GUI界面

### 2. 网络要求
- 需要访问内网素材管理系统
- 稳定的网络连接
- 足够的下载带宽

### 3. 文件权限
- data目录需要读写权限
- Excel文件不能被其他程序占用
- 临时文件夹需要写入权限

### 4. 性能考虑
- 大量数据处理可能需要时间
- 建议定期清理临时文件
- 避免频繁更新操作

## 故障排除

### 常见问题
1. **Playwright未安装**: 安装playwright并下载浏览器
2. **网络连接失败**: 检查内网连接和VPN
3. **文件权限错误**: 关闭Excel文件，检查目录权限
4. **数据加载失败**: 检查avater_list.xlsx文件是否损坏

### 解决方案
- 重新安装依赖
- 检查网络设置
- 重启程序
- 删除损坏的数据文件重新开始

## 最新更新

### 素材更新功能 ✅
- **完成时间**: 2024-12-01
- **功能**: 完整的自动下载和数据处理流程
- **特点**: 智能去重、列映射、增量更新

### 界面优化 ✅
- **搜索功能**: 完整的搜索导航体验
- **表格显示**: 美观的边框和样式设计
- **日志系统**: 详细的操作日志记录

现在视频管理模块已经具备完整的素材更新功能，可以高效地管理和更新视频素材数据！
