#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
设置对话框类，用于配置应用程序设置
"""

import os
from PySide6.QtCore import Qt, Slot, QEvent
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QComboBox, QSpinBox, QCheckBox,
    QPushButton, QFileDialog, QGroupBox, QFormLayout,
    QScrollArea, QWidget, QMessageBox, QApplication, QFrame
)

from core.config_manager import ConfigManager
from ui.custom_widgets import CustomCheckBox


class SettingsDialog(QDialog):
    """设置对话框类"""
    
    def __init__(self, config_manager, parent=None):
        """初始化设置对话框"""
        super().__init__(parent)
        
        # 配置管理器
        self.config_manager = config_manager
        
        # 设置窗口属性
        self.setup_window()
        
        # 创建UI组件
        self.create_widgets()
        
        # 创建布局
        self.create_layout()

        # 应用样式
        self.apply_styles()

        # 加载当前设置
        self.load_settings()
    
    def setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle("设置")
        self.resize(650, 700)  # 增加高度从500到700
        self.setMinimumSize(600, 650)  # 增加最小高度从450到650
        
        # 应用样式
        self.setObjectName("settingsDialog")
    
    def create_widgets(self):
        """创建UI组件"""
        # 创建滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # 创建滚动内容容器
        self.scroll_widget = QWidget()
        self.scroll_area.setWidget(self.scroll_widget)

        # 创建各个设置组
        self.create_general_group()
        self.create_voice_clone_group()
        self.create_digital_human_group()

        # 创建按钮
        self.create_buttons()

    def create_general_group(self):
        """创建通用设置组"""
        self.group_general = QGroupBox("通用设置")
        self.group_general.setObjectName("settingsGroup")
        layout = QFormLayout(self.group_general)
        layout.setVerticalSpacing(15)
        layout.setHorizontalSpacing(20)

        # 启用剪切板监听
        self.chk_clipboard_monitor = CustomCheckBox("启用剪切板监听")
        layout.addRow("", self.chk_clipboard_monitor)

        # 启用夜间模式
        self.chk_night_mode = CustomCheckBox("启用夜间模式")
        layout.addRow("", self.chk_night_mode)

    def create_voice_clone_group(self):
        """创建声音克隆设置组"""
        self.group_voice_clone = QGroupBox("声音克隆设置")
        self.group_voice_clone.setObjectName("settingsGroup")
        layout = QFormLayout(self.group_voice_clone)
        layout.setVerticalSpacing(15)
        layout.setHorizontalSpacing(20)

        # Fish Audio API设置
        api_label = QLabel("Fish Audio API设置")
        api_label.setObjectName("settingsSubTitle")
        layout.addRow("", api_label)

        # API密钥
        self.txt_api_key = QLineEdit()
        self.txt_api_key.setObjectName("settingsLineEdit")
        self.txt_api_key.setEchoMode(QLineEdit.Password)
        self.txt_api_key.setPlaceholderText("输入Fish Audio API密钥")
        layout.addRow("API密钥:", self.txt_api_key)

        # API URL
        self.txt_api_endpoint = QLineEdit()
        self.txt_api_endpoint.setObjectName("settingsLineEdit")
        self.txt_api_endpoint.setPlaceholderText("https://api.fish.audio/v1/tts")
        layout.addRow("API URL:", self.txt_api_endpoint)

        # 声音模型
        self.cmb_voice_model = QComboBox()
        self.cmb_voice_model.setObjectName("settingsComboBox")
        self.cmb_voice_model.addItems(["speech-1.6", "speech-1.5", "speech-1.0"])
        layout.addRow("声音模型:", self.cmb_voice_model)

        # 声音ID设置
        voice_id_label = QLabel("声音ID设置")
        voice_id_label.setObjectName("settingsSubTitle")
        layout.addRow("", voice_id_label)

        # 知识库URL
        self.txt_knowledge_url = QLineEdit()
        self.txt_knowledge_url.setObjectName("settingsLineEdit")
        self.txt_knowledge_url.setPlaceholderText("https://ku.baidu-int.com/knowledge/...")
        layout.addRow("知识库URL:", self.txt_knowledge_url)

        # 知识库API
        self.txt_knowledge_api = QLineEdit()
        self.txt_knowledge_api.setObjectName("settingsLineEdit")
        self.txt_knowledge_api.setEchoMode(QLineEdit.Password)
        self.txt_knowledge_api.setPlaceholderText("输入知识库API密钥")
        layout.addRow("知识库API:", self.txt_knowledge_api)

        # 处理设置
        process_label = QLabel("处理设置")
        process_label.setObjectName("settingsSubTitle")
        layout.addRow("", process_label)

        # 并发数量
        self.spn_concurrent = QSpinBox()
        self.spn_concurrent.setObjectName("settingsSpinBox")
        self.spn_concurrent.setMinimum(1)
        self.spn_concurrent.setMaximum(5)
        self.spn_concurrent.setValue(3)
        layout.addRow("并发数量:", self.spn_concurrent)

    def create_digital_human_group(self):
        """创建数字人设置组"""
        self.group_digital_human = QGroupBox("数字人设置")
        self.group_digital_human.setObjectName("settingsGroup")
        layout = QFormLayout(self.group_digital_human)
        layout.setVerticalSpacing(15)
        layout.setHorizontalSpacing(20)

        # 启用无头模式
        self.chk_headless_mode = CustomCheckBox("启用无头模式")
        self.chk_headless_mode.setChecked(True)  # 默认启用
        self.chk_headless_mode.setToolTip("启用后数字人上传将在后台运行，禁用后可以看到浏览器界面便于调试")
        layout.addRow("", self.chk_headless_mode)
        
        # 视频下载设置分隔线
        video_download_label = QLabel("视频下载设置")
        video_download_label.setObjectName("settingsSubTitle")
        layout.addRow("", video_download_label)
        
        # 并发浏览器数量
        self.spn_video_concurrent_browsers = QSpinBox()
        self.spn_video_concurrent_browsers.setObjectName("settingsSpinBox")
        self.spn_video_concurrent_browsers.setMinimum(1)
        self.spn_video_concurrent_browsers.setMaximum(10)
        self.spn_video_concurrent_browsers.setValue(3)
        self.spn_video_concurrent_browsers.setToolTip("视频下载时同时运行的浏览器实例数量")
        layout.addRow("并发浏览器数:", self.spn_video_concurrent_browsers)
        
        # 搜索天数
        self.spn_video_search_days = QSpinBox()
        self.spn_video_search_days.setObjectName("settingsSpinBox") 
        self.spn_video_search_days.setMinimum(1)
        self.spn_video_search_days.setMaximum(30)
        self.spn_video_search_days.setValue(3)
        self.spn_video_search_days.setToolTip("查找最近几天的创作任务文件夹")
        layout.addRow("搜索天数:", self.spn_video_search_days)
        
        # 启用截图
        self.chk_video_enable_screenshots = CustomCheckBox("启用下载截图")
        self.chk_video_enable_screenshots.setChecked(False)
        self.chk_video_enable_screenshots.setToolTip("启用后会在下载过程中保存截图，便于调试但会影响性能")
        layout.addRow("", self.chk_video_enable_screenshots)



    def create_buttons(self):
        """创建按钮"""
        # 创建按钮布局
        self.button_layout = QHBoxLayout()
        self.button_layout.addStretch()

        # 取消按钮
        self.btn_cancel = QPushButton("取消")
        self.btn_cancel.setObjectName("secondaryButton")
        self.btn_cancel.clicked.connect(self.reject)

        # 保存按钮
        self.btn_save = QPushButton("保存设置")
        self.btn_save.setObjectName("primaryButton")
        self.btn_save.clicked.connect(self.save_settings)

        # 添加到布局
        self.button_layout.addWidget(self.btn_cancel)
        self.button_layout.addWidget(self.btn_save)

    def create_layout(self):
        """创建布局"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(30, 30, 30, 30)
        main_layout.setSpacing(25)

        # 添加滚动区域
        main_layout.addWidget(self.scroll_area)

        # 创建滚动内容布局
        scroll_layout = QVBoxLayout(self.scroll_widget)
        scroll_layout.setContentsMargins(20, 20, 20, 20)
        scroll_layout.setSpacing(30)

        # 添加各个设置组
        scroll_layout.addWidget(self.group_general)
        scroll_layout.addWidget(self.group_voice_clone)
        scroll_layout.addWidget(self.group_digital_human)

        # 添加弹性空间
        scroll_layout.addStretch()

        # 添加按钮布局
        main_layout.addLayout(self.button_layout)

    def load_settings(self):
        """从配置管理器加载设置到UI"""
        # 通用设置
        self.chk_clipboard_monitor.setChecked(self.config_manager.get("enable_clipboard_monitor", False))
        self.chk_night_mode.setChecked(self.config_manager.get("dark_mode", False))

        # 声音克隆设置 - Fish Audio API
        self.txt_api_key.setText(self.config_manager.get("api_key", ""))
        self.txt_api_endpoint.setText(self.config_manager.get("api_endpoint", "https://api.fish.audio/v1/tts"))
        self.cmb_voice_model.setCurrentText(self.config_manager.get("model", ""))

        # 声音克隆设置 - 声音ID设置
        self.txt_knowledge_url.setText(self.config_manager.get("baidu_sheet_url", ""))
        self.txt_knowledge_api.setText(self.config_manager.get("baidu_sheets_token", ""))

        # 声音克隆设置 - 处理设置
        self.spn_concurrent.setValue(self.config_manager.get("max_concurrent", 3))

        # 数字人设置
        self.chk_headless_mode.setChecked(self.config_manager.get("headless_mode", True))
        
        # 视频下载设置
        self.spn_video_concurrent_browsers.setValue(self.config_manager.get("video_download_concurrent_browsers", 3))
        self.spn_video_search_days.setValue(self.config_manager.get("video_download_search_days", 3))
        self.chk_video_enable_screenshots.setChecked(self.config_manager.get("video_download_enable_screenshots", False))

    def save_settings(self):
        """将UI设置保存到配置管理器"""
        # 通用设置
        self.config_manager.set("enable_clipboard_monitor", self.chk_clipboard_monitor.isChecked())
        self.config_manager.set("dark_mode", self.chk_night_mode.isChecked())

        # 声音克隆设置 - Fish Audio API
        self.config_manager.set("api_key", self.txt_api_key.text())
        self.config_manager.set("api_endpoint", self.txt_api_endpoint.text())
        self.config_manager.set("model", self.cmb_voice_model.currentText())

        # 声音克隆设置 - 声音ID设置
        self.config_manager.set("baidu_sheet_url", self.txt_knowledge_url.text())
        self.config_manager.set("baidu_sheets_token", self.txt_knowledge_api.text())

        # 声音克隆设置 - 处理设置
        self.config_manager.set("max_concurrent", self.spn_concurrent.value())

        # 数字人设置
        self.config_manager.set("headless_mode", self.chk_headless_mode.isChecked())
        
        # 视频下载设置
        self.config_manager.set("video_download_concurrent_browsers", self.spn_video_concurrent_browsers.value())
        self.config_manager.set("video_download_search_days", self.spn_video_search_days.value())
        self.config_manager.set("video_download_enable_screenshots", self.chk_video_enable_screenshots.isChecked())

        # 保存配置
        self.config_manager.save()

        # 关闭对话框
        self.accept()

    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
                font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
                font-size: 14px;
            }

            QGroupBox {
                font-weight: bold;
                font-size: 16px;
                color: #333;
                border: 1px solid #ddd;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #3b82f6;
                background-color: white;
            }

            QLabel {
                color: #333;
                font-size: 14px;
                font-weight: normal;
            }

            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 14px;
                background-color: white;
            }

            QLineEdit:focus {
                border-color: #3b82f6;
                outline: none;
            }

            QComboBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 14px;
                background-color: white;
                min-width: 120px;
            }

            QComboBox:focus {
                border-color: #3b82f6;
            }

            QComboBox::drop-down {
                border: none;
                width: 20px;
            }

            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 6px solid #666;
                margin-right: 8px;
            }

            QComboBox::down-arrow:hover {
                border-top-color: #3b82f6;
            }

            QSpinBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px 12px;
                font-size: 14px;
                background-color: white;
                min-width: 80px;
            }

            QSpinBox:focus {
                border-color: #3b82f6;
            }

            /* 原生复选框样式已移除，使用CustomCheckBox */

            QPushButton {
                background-color: #3b82f6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: 500;
                min-width: 80px;
            }

            QPushButton:hover {
                background-color: #2563eb;
            }

            QPushButton:pressed {
                background-color: #1d4ed8;
            }

            QPushButton[objectName="secondaryButton"] {
                background-color: #f8f9fa;
                color: #333;
                border: 1px solid #ddd;
            }

            QPushButton[objectName="secondaryButton"]:hover {
                background-color: #e9ecef;
                border-color: #adb5bd;
            }

            QPushButton[objectName="secondaryButton"]:pressed {
                background-color: #dee2e6;
            }

            QScrollArea {
                border: none;
                background-color: transparent;
            }

            QScrollBar:vertical {
                background-color: #f8f9fa;
                width: 12px;
                border-radius: 6px;
            }

            QScrollBar::handle:vertical {
                background-color: #adb5bd;
                border-radius: 6px;
                min-height: 20px;
            }

            QScrollBar::handle:vertical:hover {
                background-color: #6c757d;
            }

            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
        """)
    