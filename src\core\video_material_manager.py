#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
视频素材管理器
负责从网站下载素材表格，处理数据并更新本地文件
"""

import os
import sys
import pandas as pd
import asyncio
import subprocess
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from PySide6.QtCore import QObject, Signal, QThread
import tempfile
import shutil

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

try:
    from playwright.async_api import async_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    print("⚠️ Playwright未安装，视频素材管理功能将受限")

# 尝试导入Chrome MCP工具作为备选
try:
    # 这里可以导入Chrome MCP相关工具
    # 例如：from chrome_mcp import ChromeManager
    CHROME_MCP_AVAILABLE = False  # 暂时设为False，等待具体实现
except ImportError:
    CHROME_MCP_AVAILABLE = False


class VideoMaterialManager(QObject):
    """视频素材管理器"""
    
    # 信号定义
    log_message = Signal(str)
    progress_updated = Signal(int, int)  # 当前进度, 总进度
    update_completed = Signal(bool, str)  # 是否成功, 消息
    
    def __init__(self, config_manager=None):
        super().__init__()
        self.config_manager = config_manager
        
        # 文件路径 - 使用绝对路径确保正确性
        current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.data_dir = os.path.join(current_dir, "data")
        self.temp_dir = os.path.join(self.data_dir, "temp")
        self.avatar_list_path = os.path.join(self.data_dir, "avatar_list.xlsx")

        # 调试信息
        self.log_message.emit(f"📁 数据目录: {self.data_dir}")
        self.log_message.emit(f"📄 素材文件路径: {self.avatar_list_path}")
        self.log_message.emit(f"📋 文件是否存在: {os.path.exists(self.avatar_list_path)}")
        
        # 确保目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # 网站配置
        self.website_url = "https://zxsc.baidu-int.com/ccbms/Home/WorkVideo/WorkVideoManage/Report"

        # 浏览器调试端口配置
        self.debug_port = 9222

    def check_debug_browser(self) -> bool:
        """检查调试端口浏览器是否可用"""
        try:
            import requests
            # 使用IPv4地址避免IPv6连接问题
            response = requests.get(f"http://127.0.0.1:{self.debug_port}/json", timeout=3)
            if response.status_code == 200:
                # 检查是否有可用的页面
                data = response.json()
                if data and len(data) > 0:
                    self.log_message.emit(f"🔍 检测到 {len(data)} 个浏览器页面")
                    return True
            return False
        except Exception as e:
            self.log_message.emit(f"🔍 调试端口检查异常: {str(e)}")
            return False

    def start_debug_browser(self) -> bool:
        """启动带调试端口的浏览器"""
        try:
            import subprocess
            import platform

            if platform.system() == "Windows":
                # Windows Chrome路径
                chrome_paths = [
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                    r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME'))
                ]

                chrome_path = None
                for path in chrome_paths:
                    if os.path.exists(path):
                        chrome_path = path
                        break

                if chrome_path:
                    # 首先检查是否已有Chrome进程在运行调试端口
                    chrome_running = False
                    if PSUTIL_AVAILABLE:
                        try:
                            import psutil
                            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                                if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                                    cmdline = proc.info['cmdline'] or []
                                    if any(f'--remote-debugging-port={self.debug_port}' in arg for arg in cmdline):
                                        chrome_running = True
                                        self.log_message.emit(f"🔍 检测到已有Chrome调试进程: PID {proc.info['pid']}")
                                        break
                        except Exception as e:
                            self.log_message.emit(f"🔍 进程检查异常: {str(e)}")
                    else:
                        self.log_message.emit("🔍 psutil不可用，跳过进程检查")

                    if not chrome_running:
                        # 首先关闭所有Chrome进程，确保干净启动
                        try:
                            if PSUTIL_AVAILABLE:
                                import psutil
                                for proc in psutil.process_iter(['pid', 'name']):
                                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                                        try:
                                            proc.terminate()
                                            self.log_message.emit(f"🔄 关闭现有Chrome进程: PID {proc.info['pid']}")
                                        except Exception:
                                            pass
                                # 等待进程关闭
                                import time
                                time.sleep(2)
                        except Exception:
                            pass

                        # 改进的启动参数，确保调试端口正确开启
                        startup_args = [
                            chrome_path,
                            f"--remote-debugging-port={self.debug_port}",
                            f"--remote-debugging-address=127.0.0.1",
                            "--user-data-dir=chrome-debug-profile",
                            "--no-first-run",
                            "--no-default-browser-check",
                            "--disable-web-security",
                            "--disable-features=VizDisplayCompositor",
                            "--disable-background-timer-throttling",
                            "--disable-backgrounding-occluded-windows",
                            "--disable-renderer-backgrounding",
                            self.website_url  # 直接打开目标网站
                        ]

                        # 启动Chrome进程
                        self.log_message.emit(f"🚀 启动Chrome命令: {' '.join(startup_args[:6])}...")
                        subprocess.Popen(startup_args)
                        self.log_message.emit(f"🚀 已启动Chrome调试实例，端口: {self.debug_port}")
                        self.log_message.emit(f"🌐 已打开目标网站: {self.website_url}")
                    else:
                        self.log_message.emit(f"🔍 Chrome调试进程已在运行，尝试打开新标签页")
                        # 如果Chrome已在运行，尝试打开新标签页
                        subprocess.Popen([chrome_path, "--new-tab", self.website_url])

                    return True
                else:
                    self.log_message.emit("❌ 未找到Chrome浏览器")
                    return False
            else:
                # macOS/Linux
                subprocess.Popen([
                    "google-chrome",
                    f"--remote-debugging-port={self.debug_port}",
                    "--user-data-dir=chrome-debug-profile",
                    "--no-first-run",
                    "--no-default-browser-check",
                    "--new-window",
                    self.website_url
                ])
                return True

        except Exception as e:
            self.log_message.emit(f"❌ 启动调试浏览器失败: {str(e)}")
            return False
        
    def get_recent_week_data(self) -> pd.DataFrame:
        """获取最近7天的数据（只使用更新日期列，更新日期为空的不显示）"""
        try:
            if not os.path.exists(self.avatar_list_path):
                self.log_message.emit("avatar_list.xlsx文件不存在，返回空数据")
                return pd.DataFrame()

            # 读取现有数据
            df = pd.read_excel(self.avatar_list_path)
            self.log_message.emit(f"📊 读取文件成功，总计 {len(df)} 条记录")

            # 只使用更新日期列进行筛选
            if '更新日期' not in df.columns:
                self.log_message.emit("❌ 未找到'更新日期'列，返回空数据")
                return pd.DataFrame()

            # 转换更新日期列为datetime
            df['更新日期'] = pd.to_datetime(df['更新日期'], errors='coerce')

            # 统计更新日期数据情况
            total_records = len(df)
            null_date_records = df['更新日期'].isna().sum()
            valid_date_records = total_records - null_date_records

            self.log_message.emit(f"📊 更新日期统计: 总计 {total_records} 条，有效更新日期 {valid_date_records} 条，空更新日期 {null_date_records} 条")

            if valid_date_records == 0:
                self.log_message.emit("📋 所有记录的更新日期都为空，返回空数据")
                return pd.DataFrame()

            # 计算7天前的日期（精确到天）
            seven_days_ago = datetime.now() - timedelta(days=7)
            seven_days_ago = seven_days_ago.replace(hour=0, minute=0, second=0, microsecond=0)

            self.log_message.emit(f"📅 筛选条件: {seven_days_ago.strftime('%Y-%m-%d')} 之后的更新日期")

            # 过滤条件：
            # 1. 更新日期不为空（不是NaT）
            # 2. 更新日期在最近7天内
            recent_data = df[
                (df['更新日期'].notna()) &  # 更新日期不为空
                (df['更新日期'] >= seven_days_ago)  # 在最近7天内
            ]

            self.log_message.emit(f"✅ 筛选结果: {len(recent_data)} 条最近7天有更新日期的记录")

            # 按更新日期降序排列（最新的在前面）
            if len(recent_data) > 0:
                recent_data = recent_data.sort_values('更新日期', ascending=False)
                self.log_message.emit(f"📋 数据已按更新日期降序排列")
            else:
                self.log_message.emit("📋 没有找到最近7天有更新日期的记录")

            return recent_data

        except Exception as e:
            self.log_message.emit(f"❌ 获取最近7天数据失败: {str(e)}")
            return pd.DataFrame()
    
    def get_display_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """获取需要显示的列"""
        # 根据实际文件的列名进行映射
        column_mapping = {
            "素材ID": "ID",
            "外链BOS地址": "视频URL",
            "上传人邮箱后缀": "上传人邮箱后缀",
            "拍摄演员名称": "拍摄演员名称",
            "视频版型": "视频版型",
            "场景": "场景",
            "表现形式": "表现形式",
            "服装": "服装",
            "是否上传飞影": "是否上传飞影",
            "上传时间": "上传时间",
            "更新日期": "更新日期"  # 如果存在的话
        }

        # 创建显示用的DataFrame
        display_df = pd.DataFrame()

        # 按映射关系添加列
        for original_col, display_col in column_mapping.items():
            if original_col in df.columns:
                display_df[display_col] = df[original_col]

        # 如果没有"是否上传飞影"列，添加空列
        if "是否上传飞影" not in display_df.columns:
            display_df["是否上传飞影"] = ""

        # 确保有日期列用于显示
        if "更新日期" not in display_df.columns and "上传时间" in display_df.columns:
            display_df["更新日期"] = display_df["上传时间"]

        self.log_message.emit(f"📋 显示列映射完成，包含 {len(display_df.columns)} 列")

        return display_df
    
    async def download_material_data(self) -> bool:
        """从网站下载素材数据"""
        if not PLAYWRIGHT_AVAILABLE:
            self.log_message.emit("❌ Playwright未安装，无法下载素材数据")
            return False
        
        try:
            self.log_message.emit("🚀 开始下载素材数据...")

            # 检查调试端口浏览器
            if not self.check_debug_browser():
                self.log_message.emit("🔧 调试端口浏览器未运行，正在自动启动...")
                self.log_message.emit("💡 程序将自动打开Chrome浏览器窗口")

                if not self.start_debug_browser():
                    self.log_message.emit("⚠️ 启动调试浏览器失败，将使用普通模式")
                else:
                    # 等待浏览器启动并检查连接
                    import time
                    self.log_message.emit("⏳ 等待Chrome调试端口启动...")
                    self.log_message.emit("📋 请在打开的浏览器中完成登录（如需要）")

                    # 增加等待时间到30秒，Chrome启动调试端口需要更多时间
                    for i in range(30):
                        time.sleep(1)
                        if self.check_debug_browser():
                            self.log_message.emit(f"✅ Chrome调试端口启动成功 (等待{i+1}秒)")
                            break
                        if i == 4:
                            self.log_message.emit("💡 Chrome正在启动调试端口，请稍候...")
                        elif i == 9:
                            self.log_message.emit("⏳ 仍在等待调试端口响应...")
                        elif i == 19:
                            self.log_message.emit("⏳ 调试端口启动较慢，继续等待...")
                        elif i % 5 == 0 and i > 0:
                            self.log_message.emit(f"⏳ 等待中... ({i+1}/30)")
                    else:
                        self.log_message.emit("⚠️ 调试端口启动超时，但将继续尝试连接")
            else:
                self.log_message.emit("✅ 检测到调试端口浏览器正在运行")

            async with async_playwright() as p:
                # 尝试连接到调试端口的浏览器
                try:
                    self.log_message.emit(f"🔗 尝试连接到调试端口 {self.debug_port}...")
                    # 使用IPv4地址避免IPv6连接问题
                    browser = await p.chromium.connect_over_cdp(f"http://127.0.0.1:{self.debug_port}")
                    self.log_message.emit("✅ 成功连接到现有浏览器实例")
                except Exception as e:
                    self.log_message.emit(f"⚠️ 连接调试端口失败: {str(e)}")
                    self.log_message.emit("🚀 启动新的浏览器实例...")
                    # 如果连接失败，启动新的浏览器实例
                    browser = await p.chromium.launch(
                        headless=False,
                        args=[f"--remote-debugging-port={self.debug_port}"]
                    )

                # 获取或创建页面
                contexts = browser.contexts
                if contexts:
                    # 使用现有的上下文
                    context = contexts[0]
                    pages = context.pages
                    if pages:
                        page = pages[0]
                        self.log_message.emit("📄 使用现有页面")
                    else:
                        page = await context.new_page()
                        self.log_message.emit("📄 在现有上下文中创建新页面")
                else:
                    # 创建新的上下文和页面
                    context = await browser.new_context()
                    page = await context.new_page()
                    self.log_message.emit("📄 创建新的上下文和页面")
                
                # 导航到目标页面
                self.log_message.emit(f"📱 正在访问: {self.website_url}")
                await page.goto(self.website_url)
                
                # 等待页面加载
                await page.wait_for_timeout(3000)
                
                # 1. 点击素材创建日期的选择框
                self.log_message.emit("📅 设置素材创建日期...")
                date_selector = ".el-date-editor.el-range-editor"
                await page.click(date_selector)
                await page.wait_for_timeout(1000)
                
                # 选择"最近一年"选项
                try:
                    # 查找并点击"最近一年"选项
                    recent_year_option = await page.wait_for_selector("text=最近一年", timeout=5000)
                    await recent_year_option.click()
                    self.log_message.emit("✅ 已选择最近一年")
                    await page.wait_for_timeout(1000)
                except Exception as e:
                    self.log_message.emit(f"⚠️ 选择最近一年失败: {str(e)}")
                
                # 2. 点击右侧的下拉选择框（剪辑素材库 -> 拍摄素材库）
                self.log_message.emit("📂 设置素材库类型...")
                try:
                    # 查找第二个下拉框
                    dropdown_selectors = await page.query_selector_all(".el-select")
                    if len(dropdown_selectors) >= 2:
                        await dropdown_selectors[1].click()
                        await page.wait_for_timeout(1000)
                        
                        # 选择"拍摄素材库"
                        shooting_option = await page.wait_for_selector("text=拍摄素材库", timeout=5000)
                        await shooting_option.click()
                        self.log_message.emit("✅ 已选择拍摄素材库")
                        await page.wait_for_timeout(1000)
                    else:
                        self.log_message.emit("⚠️ 未找到素材库选择框")
                except Exception as e:
                    self.log_message.emit(f"⚠️ 设置素材库类型失败: {str(e)}")
                
                # 3. 点击导出按钮
                self.log_message.emit("📤 开始导出数据...")
                try:
                    export_button = await page.wait_for_selector(".el-button.el-button--primary", timeout=5000)
                    
                    # 设置下载监听
                    download_path = None
                    
                    async def handle_download(download):
                        nonlocal download_path
                        # 保存到临时目录
                        download_path = os.path.join(self.temp_dir, f"material_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
                        await download.save_as(download_path)
                        self.log_message.emit(f"📁 文件已下载到: {download_path}")
                    
                    page.on("download", handle_download)
                    
                    # 点击导出按钮
                    await export_button.click()
                    self.log_message.emit("✅ 已点击导出按钮，等待下载...")
                    
                    # 等待下载完成
                    await page.wait_for_timeout(10000)  # 等待10秒
                    
                    await browser.close()
                    
                    if download_path and os.path.exists(download_path):
                        self.log_message.emit("✅ 素材数据下载完成")
                        return await self.process_downloaded_data(download_path)
                    else:
                        self.log_message.emit("❌ 下载失败，未找到下载文件")
                        return False
                        
                except Exception as e:
                    self.log_message.emit(f"❌ 导出失败: {str(e)}")
                    await browser.close()
                    return False
                    
        except Exception as e:
            self.log_message.emit(f"❌ 下载素材数据失败: {str(e)}")
            return False
    
    async def process_downloaded_data(self, downloaded_file_path: str) -> bool:
        """处理下载的数据文件"""
        try:
            self.log_message.emit("🔄 开始处理下载的数据...")
            
            # 读取下载的文件
            new_df = pd.read_excel(downloaded_file_path)
            self.log_message.emit(f"📊 下载文件包含 {len(new_df)} 条记录")
            
            # 根据A列（素材ID）进行去重
            if '素材ID' in new_df.columns:
                original_count = len(new_df)
                new_df = new_df.drop_duplicates(subset=['素材ID'], keep='first')
                dedup_count = len(new_df)
                self.log_message.emit(f"🔄 去重处理: {original_count} -> {dedup_count} 条记录")
            else:
                self.log_message.emit("⚠️ 未找到素材ID列，跳过去重")
            
            # 处理现有的avatar_list.xlsx文件
            return await self.merge_with_existing_data(new_df)
            
        except Exception as e:
            self.log_message.emit(f"❌ 处理下载数据失败: {str(e)}")
            return False
    
    async def merge_with_existing_data(self, new_df: pd.DataFrame) -> bool:
        """与现有数据合并"""
        try:
            self.log_message.emit("🔄 开始与现有数据合并...")
            
            # 读取现有数据
            if os.path.exists(self.avatar_list_path):
                existing_df = pd.read_excel(self.avatar_list_path)
                self.log_message.emit(f"📊 现有数据包含 {len(existing_df)} 条记录")
            else:
                existing_df = pd.DataFrame()
                self.log_message.emit("📊 创建新的数据文件")
            
            # 列名映射
            column_mapping = {
                '素材ID': 'ID',
                '外链BOS地址': '视频URL'
            }
            
            # 重命名新数据的列
            new_df_renamed = new_df.rename(columns=column_mapping)
            
            # 添加默认列
            if '是否上传飞影' not in new_df_renamed.columns:
                new_df_renamed['是否上传飞影'] = ''
            
            # 添加更新日期
            new_df_renamed['更新日期'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 如果现有数据为空，直接使用新数据
            if existing_df.empty:
                result_df = new_df_renamed
                new_count = len(result_df)
            else:
                # 找出新增的记录（ID不存在于现有数据中）
                if 'ID' in existing_df.columns and 'ID' in new_df_renamed.columns:
                    existing_ids = set(existing_df['ID'].astype(str))
                    new_records = new_df_renamed[~new_df_renamed['ID'].astype(str).isin(existing_ids)]
                    new_count = len(new_records)
                    
                    if new_count > 0:
                        # 将新记录添加到现有数据的最上面
                        result_df = pd.concat([new_records, existing_df], ignore_index=True)
                        self.log_message.emit(f"✅ 新增 {new_count} 条记录")
                    else:
                        result_df = existing_df
                        self.log_message.emit("ℹ️ 没有新增记录")
                else:
                    self.log_message.emit("⚠️ 缺少ID列，无法进行增量更新")
                    return False
            
            # 保存更新后的数据
            result_df.to_excel(self.avatar_list_path, index=False)
            self.log_message.emit(f"💾 数据已保存，总计 {len(result_df)} 条记录")
            
            # 清理临时文件
            try:
                if os.path.exists(downloaded_file_path):
                    os.remove(downloaded_file_path)
                    self.log_message.emit("🗑️ 已清理临时文件")
            except Exception as e:
                self.log_message.emit(f"⚠️ 清理临时文件失败: {str(e)}")
            
            self.update_completed.emit(True, f"素材更新完成，新增 {new_count} 条记录")
            return True
            
        except Exception as e:
            self.log_message.emit(f"❌ 数据合并失败: {str(e)}")
            self.update_completed.emit(False, f"数据合并失败: {str(e)}")
            return False


class MaterialUpdateWorker(QThread):
    """素材更新工作线程"""
    
    def __init__(self, material_manager):
        super().__init__()
        self.material_manager = material_manager
    
    def run(self):
        """运行素材更新"""
        try:
            # 在新的事件循环中运行异步任务
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            success = loop.run_until_complete(
                self.material_manager.download_material_data()
            )
            
            loop.close()
            
        except Exception as e:
            self.material_manager.log_message.emit(f"❌ 素材更新线程异常: {str(e)}")
            self.material_manager.update_completed.emit(False, f"更新失败: {str(e)}")
