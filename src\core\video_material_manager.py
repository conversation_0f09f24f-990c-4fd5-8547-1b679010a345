#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
视频素材管理器
负责从网站下载素材表格，处理数据并更新本地文件
"""

import os
import sys
import pandas as pd
import asyncio
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from PySide6.QtCore import QObject, Signal, QThread
import tempfile
import shutil

try:
    from playwright.async_api import async_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    print("⚠️ Playwright未安装，视频素材管理功能将受限")


class VideoMaterialManager(QObject):
    """视频素材管理器"""
    
    # 信号定义
    log_message = Signal(str)
    progress_updated = Signal(int, int)  # 当前进度, 总进度
    update_completed = Signal(bool, str)  # 是否成功, 消息
    
    def __init__(self, config_manager=None):
        super().__init__()
        self.config_manager = config_manager
        
        # 文件路径
        self.data_dir = os.path.join(os.getcwd(), "data")
        self.temp_dir = os.path.join(self.data_dir, "temp")
        self.avatar_list_path = os.path.join(self.data_dir, "avater_list.xlsx")
        
        # 确保目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # 网站配置
        self.website_url = "https://zxsc.baidu-int.com/ccbms/Home/WorkVideo/WorkVideoManage/Report"
        
    def get_recent_week_data(self) -> pd.DataFrame:
        """获取最近一周的数据"""
        try:
            if not os.path.exists(self.avatar_list_path):
                self.log_message.emit("avatar_list.xlsx文件不存在，返回空数据")
                return pd.DataFrame()
            
            # 读取现有数据
            df = pd.read_excel(self.avatar_list_path)
            
            # 检查是否有更新日期列
            if '更新日期' not in df.columns:
                self.log_message.emit("未找到更新日期列，返回所有数据")
                return df
            
            # 计算一周前的日期
            one_week_ago = datetime.now() - timedelta(days=7)
            
            # 过滤最近一周的数据
            df['更新日期'] = pd.to_datetime(df['更新日期'], errors='coerce')
            recent_data = df[df['更新日期'] >= one_week_ago]
            
            self.log_message.emit(f"加载最近一周数据: {len(recent_data)} 条记录")
            return recent_data
            
        except Exception as e:
            self.log_message.emit(f"获取最近一周数据失败: {str(e)}")
            return pd.DataFrame()
    
    def get_display_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """获取需要显示的列"""
        display_columns = [
            "ID", "视频URL", "上传人邮箱后缀", "拍摄演员名称", 
            "视频版型", "场景", "表现形式", "服装", "是否上传飞影", "更新日期"
        ]
        
        # 只保留存在的列
        existing_columns = [col for col in display_columns if col in df.columns]
        
        if existing_columns:
            return df[existing_columns].copy()
        else:
            self.log_message.emit("未找到任何显示列，返回空数据")
            return pd.DataFrame()
    
    async def download_material_data(self) -> bool:
        """从网站下载素材数据"""
        if not PLAYWRIGHT_AVAILABLE:
            self.log_message.emit("❌ Playwright未安装，无法下载素材数据")
            return False
        
        try:
            self.log_message.emit("🚀 开始下载素材数据...")
            
            async with async_playwright() as p:
                # 启动浏览器
                browser = await p.chromium.launch(headless=False)
                page = await browser.new_page()
                
                # 导航到目标页面
                self.log_message.emit(f"📱 正在访问: {self.website_url}")
                await page.goto(self.website_url)
                
                # 等待页面加载
                await page.wait_for_timeout(3000)
                
                # 1. 点击素材创建日期的选择框
                self.log_message.emit("📅 设置素材创建日期...")
                date_selector = ".el-date-editor.el-range-editor"
                await page.click(date_selector)
                await page.wait_for_timeout(1000)
                
                # 选择"最近一年"选项
                try:
                    # 查找并点击"最近一年"选项
                    recent_year_option = await page.wait_for_selector("text=最近一年", timeout=5000)
                    await recent_year_option.click()
                    self.log_message.emit("✅ 已选择最近一年")
                    await page.wait_for_timeout(1000)
                except Exception as e:
                    self.log_message.emit(f"⚠️ 选择最近一年失败: {str(e)}")
                
                # 2. 点击右侧的下拉选择框（剪辑素材库 -> 拍摄素材库）
                self.log_message.emit("📂 设置素材库类型...")
                try:
                    # 查找第二个下拉框
                    dropdown_selectors = await page.query_selector_all(".el-select")
                    if len(dropdown_selectors) >= 2:
                        await dropdown_selectors[1].click()
                        await page.wait_for_timeout(1000)
                        
                        # 选择"拍摄素材库"
                        shooting_option = await page.wait_for_selector("text=拍摄素材库", timeout=5000)
                        await shooting_option.click()
                        self.log_message.emit("✅ 已选择拍摄素材库")
                        await page.wait_for_timeout(1000)
                    else:
                        self.log_message.emit("⚠️ 未找到素材库选择框")
                except Exception as e:
                    self.log_message.emit(f"⚠️ 设置素材库类型失败: {str(e)}")
                
                # 3. 点击导出按钮
                self.log_message.emit("📤 开始导出数据...")
                try:
                    export_button = await page.wait_for_selector(".el-button.el-button--primary", timeout=5000)
                    
                    # 设置下载监听
                    download_path = None
                    
                    async def handle_download(download):
                        nonlocal download_path
                        # 保存到临时目录
                        download_path = os.path.join(self.temp_dir, f"material_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
                        await download.save_as(download_path)
                        self.log_message.emit(f"📁 文件已下载到: {download_path}")
                    
                    page.on("download", handle_download)
                    
                    # 点击导出按钮
                    await export_button.click()
                    self.log_message.emit("✅ 已点击导出按钮，等待下载...")
                    
                    # 等待下载完成
                    await page.wait_for_timeout(10000)  # 等待10秒
                    
                    await browser.close()
                    
                    if download_path and os.path.exists(download_path):
                        self.log_message.emit("✅ 素材数据下载完成")
                        return await self.process_downloaded_data(download_path)
                    else:
                        self.log_message.emit("❌ 下载失败，未找到下载文件")
                        return False
                        
                except Exception as e:
                    self.log_message.emit(f"❌ 导出失败: {str(e)}")
                    await browser.close()
                    return False
                    
        except Exception as e:
            self.log_message.emit(f"❌ 下载素材数据失败: {str(e)}")
            return False
    
    async def process_downloaded_data(self, downloaded_file_path: str) -> bool:
        """处理下载的数据文件"""
        try:
            self.log_message.emit("🔄 开始处理下载的数据...")
            
            # 读取下载的文件
            new_df = pd.read_excel(downloaded_file_path)
            self.log_message.emit(f"📊 下载文件包含 {len(new_df)} 条记录")
            
            # 根据A列（素材ID）进行去重
            if '素材ID' in new_df.columns:
                original_count = len(new_df)
                new_df = new_df.drop_duplicates(subset=['素材ID'], keep='first')
                dedup_count = len(new_df)
                self.log_message.emit(f"🔄 去重处理: {original_count} -> {dedup_count} 条记录")
            else:
                self.log_message.emit("⚠️ 未找到素材ID列，跳过去重")
            
            # 处理现有的avatar_list.xlsx文件
            return await self.merge_with_existing_data(new_df)
            
        except Exception as e:
            self.log_message.emit(f"❌ 处理下载数据失败: {str(e)}")
            return False
    
    async def merge_with_existing_data(self, new_df: pd.DataFrame) -> bool:
        """与现有数据合并"""
        try:
            self.log_message.emit("🔄 开始与现有数据合并...")
            
            # 读取现有数据
            if os.path.exists(self.avatar_list_path):
                existing_df = pd.read_excel(self.avatar_list_path)
                self.log_message.emit(f"📊 现有数据包含 {len(existing_df)} 条记录")
            else:
                existing_df = pd.DataFrame()
                self.log_message.emit("📊 创建新的数据文件")
            
            # 列名映射
            column_mapping = {
                '素材ID': 'ID',
                '外链BOS地址': '视频URL'
            }
            
            # 重命名新数据的列
            new_df_renamed = new_df.rename(columns=column_mapping)
            
            # 添加默认列
            if '是否上传飞影' not in new_df_renamed.columns:
                new_df_renamed['是否上传飞影'] = ''
            
            # 添加更新日期
            new_df_renamed['更新日期'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 如果现有数据为空，直接使用新数据
            if existing_df.empty:
                result_df = new_df_renamed
                new_count = len(result_df)
            else:
                # 找出新增的记录（ID不存在于现有数据中）
                if 'ID' in existing_df.columns and 'ID' in new_df_renamed.columns:
                    existing_ids = set(existing_df['ID'].astype(str))
                    new_records = new_df_renamed[~new_df_renamed['ID'].astype(str).isin(existing_ids)]
                    new_count = len(new_records)
                    
                    if new_count > 0:
                        # 将新记录添加到现有数据的最上面
                        result_df = pd.concat([new_records, existing_df], ignore_index=True)
                        self.log_message.emit(f"✅ 新增 {new_count} 条记录")
                    else:
                        result_df = existing_df
                        self.log_message.emit("ℹ️ 没有新增记录")
                else:
                    self.log_message.emit("⚠️ 缺少ID列，无法进行增量更新")
                    return False
            
            # 保存更新后的数据
            result_df.to_excel(self.avatar_list_path, index=False)
            self.log_message.emit(f"💾 数据已保存，总计 {len(result_df)} 条记录")
            
            # 清理临时文件
            try:
                if os.path.exists(downloaded_file_path):
                    os.remove(downloaded_file_path)
                    self.log_message.emit("🗑️ 已清理临时文件")
            except Exception as e:
                self.log_message.emit(f"⚠️ 清理临时文件失败: {str(e)}")
            
            self.update_completed.emit(True, f"素材更新完成，新增 {new_count} 条记录")
            return True
            
        except Exception as e:
            self.log_message.emit(f"❌ 数据合并失败: {str(e)}")
            self.update_completed.emit(False, f"数据合并失败: {str(e)}")
            return False


class MaterialUpdateWorker(QThread):
    """素材更新工作线程"""
    
    def __init__(self, material_manager):
        super().__init__()
        self.material_manager = material_manager
    
    def run(self):
        """运行素材更新"""
        try:
            # 在新的事件循环中运行异步任务
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            success = loop.run_until_complete(
                self.material_manager.download_material_data()
            )
            
            loop.close()
            
        except Exception as e:
            self.material_manager.log_message.emit(f"❌ 素材更新线程异常: {str(e)}")
            self.material_manager.update_completed.emit(False, f"更新失败: {str(e)}")
