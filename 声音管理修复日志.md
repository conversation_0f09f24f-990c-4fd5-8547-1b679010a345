# 声音管理功能修复日志

## 修复时间
2024年12月 - 声音管理功能上传问题修复

## 问题描述

### 1. 设置按钮跳转问题
- **问题**: 点击设置按钮无法正常跳转到设置页面
- **原因**: 添加声音管理页面后，设置页面的索引发生变化
- **修复**: 将设置页面索引从2更新为3

### 2. API密钥配置问题
- **问题**: 声音管理无法读取API密钥，提示"请先在设置中配置Fish Audio API密钥"
- **原因**: 使用了错误的配置键名 `fish_api_key`，实际应该使用 `api_key`
- **修复**: 统一使用声音克隆模块的API密钥配置

### 3. 音频上传失败问题
- **问题1**: 上传音频到Fish Audio失败，返回422错误
- **错误信息**:
  ```
  [
    {
      "type": "literal_error",
      "loc": ["type"],
      "msg": "Input should be 'tts'",
      "input": "svc"
    },
    {
      "type": "missing",
      "loc": ["voices"],
      "msg": "Field required"
    }
  ]
  ```

- **问题2**: 变量作用域错误
- **错误信息**: `cannot access local variable 'response' where it is not associated with a value`
- **原因**: try-except块结构不正确，response变量在except块中无法访问

- **问题3**: 异步客户端错误
- **错误信息**: `Attempted to send an sync request with an AsyncClient instance`
- **原因**: 在异步上下文中使用了同步的文件操作，导致httpx客户端混淆

## 修复方案

### 1. 设置页面索引修复
```python
# 修复前
self.content_stack.setCurrentIndex(2)

# 修复后  
self.content_stack.setCurrentIndex(3)
```

### 2. API密钥配置修复
```python
# 修复前
api_key = self.config_manager.get("fish_api_key", "")

# 修复后
api_key = self.config_manager.get("api_key", "")
```

### 3. 音频上传API修复

#### 参数修正
- **type**: `'svc'` → `'tts'`
- **文件字段**: `'file'` → `'voices'`
- **数据格式**: 字典格式 → 列表格式

#### 代码结构修正
- **修复try-except块结构**: 确保变量作用域正确
- **文件资源管理**: 使用finally块确保文件正确关闭
- **异常处理**: 改进错误处理逻辑

#### 修复前的代码
```python
files = {
    'file': (filename, f, 'audio/mpeg')
}

data = {
    'title': os.path.splitext(filename)[0],
    'type': 'svc',
    'visibility': 'private',
    'train_mode': 'fast'
}
```

#### 最终修复后的代码（httpx标准格式）
```python
# 使用httpx标准的multipart/form-data格式
data = {
    'title': os.path.splitext(filename)[0],
    'type': 'tts',
    'visibility': 'private',
    'train_mode': 'fast',
    'enhance_audio_quality': 'true'
}

files = {
    'voices': open(file_path, 'rb')
}

# 异步POST请求
response = await client.post(
    self.API_ENDPOINT,
    data=data,
    files=files,
    headers={'Authorization': f'Bearer {api_key}'}
)
```

## API文档参考

根据Fish Audio官方文档：
- 文档地址: https://docs.fish.audio/text-to-speech/create-model
- API端点: `POST https://api.fish.audio/model`
- 内容类型: `multipart/form-data`

### 正确的API调用格式
```python
response = requests.post(
    "https://api.fish.audio/model",
    files=[
        ("voices", open("audio.mp3", "rb")),
    ],
    data=[
        ("visibility", "private"),
        ("type", "tts"),
        ("title", "Demo"),
        ("train_mode", "fast"),
        ("enhance_audio_quality", "true"),
    ],
    headers={
        "Authorization": "Bearer YOUR_API_KEY",
    },
)
```

## 测试验证

### 1. API密钥读取测试
- ✅ 配置管理器正常工作
- ✅ API密钥正确读取
- ✅ 与声音克隆模块共享配置

### 2. 上传格式测试
- ✅ 文件参数格式正确
- ✅ 数据参数格式正确
- ✅ API调用格式符合文档要求

## 功能状态

### ✅ 已修复
1. 设置按钮正常跳转
2. API密钥正确读取
3. 音频上传格式正确
4. 表格显示简化为5列
5. 开始上传功能完整

### 🔄 待实现
1. ID同步到百度表格功能
2. 高级搜索和筛选功能
3. 批量操作功能

## 使用说明

1. 运行程序：`python src/main.py`
2. 确认设置中已配置Fish Audio API密钥
3. 点击左侧"声音管理"按钮
4. 使用"音频位置"打开音频文件夹
5. 放入音频文件后点击"开始上传"
6. 等待上传完成，查看表格中的新模型

## 注意事项

1. **音频格式**: 支持MP3、WAV、FLAC、M4A、OGG格式
2. **文件大小**: 建议单个文件不超过50MB
3. **网络连接**: 上传需要稳定的网络连接
4. **API限制**: 注意Fish Audio的API调用频率限制
