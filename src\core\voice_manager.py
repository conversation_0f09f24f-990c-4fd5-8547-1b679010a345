"""
声音管理器模块
用于管理Fish Audio声音模型的获取、存储和同步
"""

import os
import json
import httpx
import asyncio
from datetime import datetime
from typing import List, Dict, Optional
from PySide6.QtCore import QObject, Signal, QTimer
from PySide6.QtWidgets import QMessageBox


class VoiceManager(QObject):
    """声音管理器类"""
    
    # 信号定义
    log_message = Signal(str)  # 日志消息信号
    progress_updated = Signal(int)  # 进度更新信号
    table_updated = Signal()  # 表格更新信号
    fetch_completed = Signal(int)  # 获取/上传完成信号，参数为新增数量
    upload_completed = Signal(int)  # 上传完成信号，参数为上传数量
    
    def __init__(self):
        super().__init__()
        
        # 获取项目根目录
        current_dir = os.path.dirname(os.path.abspath(__file__))  # core目录
        src_dir = os.path.dirname(current_dir)  # src目录
        self.project_root = os.path.dirname(src_dir)  # 项目根目录
        
        # 配置项
        self.VOICE_DATA_DIR = os.path.join(self.project_root, "voice_management")
        self.VOICE_DATA_FILE = os.path.join(self.VOICE_DATA_DIR, "voice_models.json")
        self.AUDIO_UPLOAD_DIR = os.path.join(self.VOICE_DATA_DIR, "audio_files")
        self.AUDIO_UPLOADED_DIR = os.path.join(self.VOICE_DATA_DIR, "uploaded_files")
        
        # Fish Audio API配置
        self.API_BASE_URL = "https://api.fish.audio"
        self.API_ENDPOINT = f"{self.API_BASE_URL}/model"
        
        # 状态变量
        self.is_fetching = False
        self.voice_models = []  # 存储声音模型数据
        
        # 初始化目录和数据
        self._init_directories()
        self._load_voice_data()
    
    def _init_directories(self):
        """初始化必要的目录"""
        try:
            os.makedirs(self.VOICE_DATA_DIR, exist_ok=True)
            os.makedirs(self.AUDIO_UPLOAD_DIR, exist_ok=True)
            os.makedirs(self.AUDIO_UPLOADED_DIR, exist_ok=True)
            self.log_message.emit("声音管理目录初始化完成")
        except Exception as e:
            self.log_message.emit(f"目录初始化失败: {str(e)}")
    
    def _load_voice_data(self):
        """加载本地声音数据"""
        try:
            if os.path.exists(self.VOICE_DATA_FILE):
                with open(self.VOICE_DATA_FILE, 'r', encoding='utf-8') as f:
                    self.voice_models = json.load(f)
                self.log_message.emit(f"加载了 {len(self.voice_models)} 条声音数据")
            else:
                self.voice_models = []
                self.log_message.emit("未找到本地声音数据文件，将创建新文件")
        except Exception as e:
            self.log_message.emit(f"加载声音数据失败: {str(e)}")
            self.voice_models = []
    
    def _save_voice_data(self):
        """保存声音数据到本地文件"""
        try:
            with open(self.VOICE_DATA_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.voice_models, f, ensure_ascii=False, indent=2)
            self.log_message.emit("声音数据已保存到本地")
            return True
        except Exception as e:
            self.log_message.emit(f"保存声音数据失败: {str(e)}")
            return False
    
    def get_voice_models(self) -> List[Dict]:
        """获取当前的声音模型列表"""
        return self.voice_models.copy()
    
    def open_audio_folder(self):
        """打开音频文件夹"""
        try:
            import subprocess
            import platform
            
            if platform.system() == "Windows":
                os.startfile(self.AUDIO_UPLOAD_DIR)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", self.AUDIO_UPLOAD_DIR])
            else:  # Linux
                subprocess.run(["xdg-open", self.AUDIO_UPLOAD_DIR])
            
            self.log_message.emit(f"已打开音频文件夹: {self.AUDIO_UPLOAD_DIR}")
        except Exception as e:
            self.log_message.emit(f"打开音频文件夹失败: {str(e)}")
    
    def scan_audio_files(self):
        """扫描音频文件夹中的音频文件"""
        try:
            audio_files = []
            if os.path.exists(self.AUDIO_UPLOAD_DIR):
                for filename in os.listdir(self.AUDIO_UPLOAD_DIR):
                    if filename.lower().endswith(('.mp3', '.wav', '.flac', '.m4a', '.ogg')):
                        file_path = os.path.join(self.AUDIO_UPLOAD_DIR, filename)
                        if os.path.isfile(file_path):
                            audio_files.append({
                                'filename': filename,
                                'path': file_path,
                                'size': os.path.getsize(file_path)
                            })

            self.log_message.emit(f"扫描到 {len(audio_files)} 个音频文件")
            return audio_files

        except Exception as e:
            self.log_message.emit(f"扫描音频文件失败: {str(e)}")
            return []

    def move_uploaded_files(self, uploaded_files=None):
        """将上传成功的音频文件移动到已上传文件夹"""
        try:
            moved_count = 0
            files_to_move = uploaded_files if uploaded_files else []

            # 如果没有指定文件，移动所有音频文件
            if not files_to_move:
                for filename in os.listdir(self.AUDIO_UPLOAD_DIR):
                    if filename.lower().endswith(('.mp3', '.wav', '.flac', '.m4a', '.ogg')):
                        files_to_move.append(filename)

            for filename in files_to_move:
                src_path = os.path.join(self.AUDIO_UPLOAD_DIR, filename)
                if not os.path.exists(src_path):
                    continue

                dst_path = os.path.join(self.AUDIO_UPLOADED_DIR, filename)

                # 如果目标文件已存在，添加时间戳
                if os.path.exists(dst_path):
                    name, ext = os.path.splitext(filename)
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    new_filename = f"{name}_{timestamp}{ext}"
                    dst_path = os.path.join(self.AUDIO_UPLOADED_DIR, new_filename)

                os.rename(src_path, dst_path)
                moved_count += 1

            if moved_count > 0:
                self.log_message.emit(f"已移动 {moved_count} 个音频文件到已上传文件夹")
            else:
                self.log_message.emit("未找到需要移动的音频文件")

        except Exception as e:
            self.log_message.emit(f"移动音频文件失败: {str(e)}")
    
    async def fetch_voice_models_async(self, api_key: str, page_size: int = 50):
        """异步获取Fish Audio声音模型（增量获取）"""
        if self.is_fetching:
            self.log_message.emit("正在获取中，请稍候...")
            return
        
        self.is_fetching = True
        new_models_count = 0
        
        try:
            self.log_message.emit("开始获取Fish Audio声音模型...")
            
            # 获取现有模型ID集合，用于去重
            existing_ids = {model.get('modelId') for model in self.voice_models if model.get('modelId')}
            
            page_number = 1
            total_fetched = 0
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                while True:
                    # 构建请求参数
                    params = {
                        'page_size': page_size,
                        'page_number': page_number,
                        'sort_by': 'created_at'  # 按创建时间排序，便于增量获取
                    }
                    
                    # 发送API请求
                    response = await client.get(
                        self.API_ENDPOINT,
                        params=params,
                        headers={
                            'Authorization': f'Bearer {api_key}',
                            'Content-Type': 'application/json'
                        }
                    )
                    
                    if response.status_code != 200:
                        self.log_message.emit(f"API请求失败，状态码: {response.status_code}")
                        break
                    
                    data = response.json()
                    items = data.get('items', [])
                    total = data.get('total', 0)
                    
                    if not items:
                        break
                    
                    # 处理获取到的模型数据
                    for item in items:
                        model_id = item.get('_id')
                        if model_id and model_id not in existing_ids:
                            # 构建声音模型数据
                            voice_model = {
                                'name': item.get('title', '未知'),
                                'modelId': model_id,
                                'url': f"https://fish.audio/model/{model_id}",
                                'extractTime': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                'type': item.get('type', ''),
                                'description': item.get('description', ''),
                                'author': item.get('author', {}).get('nickname', ''),
                                'state': item.get('state', ''),
                                'visibility': item.get('visibility', ''),
                                'created_at': item.get('created_at', ''),
                                'like_count': item.get('like_count', 0),
                                'task_count': item.get('task_count', 0)
                            }
                            
                            self.voice_models.append(voice_model)
                            existing_ids.add(model_id)
                            new_models_count += 1
                    
                    total_fetched += len(items)
                    self.progress_updated.emit(int((total_fetched / total) * 100))
                    self.log_message.emit(f"已获取 {total_fetched}/{total} 个模型，新增 {new_models_count} 个")
                    
                    # 检查是否还有更多页面
                    if total_fetched >= total or len(items) < page_size:
                        break
                    
                    page_number += 1
                    
                    # 添加延迟避免请求过快
                    await asyncio.sleep(0.5)
            
            # 保存数据
            if new_models_count > 0:
                self._save_voice_data()
                self.table_updated.emit()
            
            self.log_message.emit(f"获取完成！新增 {new_models_count} 个声音模型")
            self.fetch_completed.emit(new_models_count)
            
        except Exception as e:
            self.log_message.emit(f"获取声音模型失败: {str(e)}")
        finally:
            self.is_fetching = False
            self.progress_updated.emit(100)
    
    def fetch_voice_models(self, api_key: str):
        """同步方法，用于从UI调用"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.fetch_voice_models_async(api_key))
        finally:
            loop.close()

    async def upload_audio_files_async(self, api_key: str, audio_files: List[Dict]):
        """异步上传音频文件到Fish Audio"""
        if self.is_fetching:
            self.log_message.emit("正在执行其他操作，请稍候...")
            return

        self.is_fetching = True
        uploaded_files = []

        try:
            self.log_message.emit(f"开始上传 {len(audio_files)} 个音频文件...")

            async with httpx.AsyncClient(timeout=60.0) as client:
                for i, audio_file in enumerate(audio_files):
                    filename = audio_file['filename']
                    file_path = audio_file['path']

                    self.log_message.emit(f"正在上传: {filename}")

                    # 准备文件数据 - 使用voices字段
                    files = [
                        ('voices', open(file_path, 'rb'))
                    ]

                    # 构建表单数据 - 使用正确的API格式
                    data = [
                        ('title', os.path.splitext(filename)[0]),  # 使用文件名作为标题
                        ('type', 'tts'),  # 文本转语音类型
                        ('visibility', 'private'),  # 私有模型
                        ('train_mode', 'fast'),  # 快速训练模式
                        ('enhance_audio_quality', 'true')  # 增强音频质量
                    ]

                    try:
                        # 发送上传请求
                        response = await client.post(
                            self.API_ENDPOINT,
                            files=files,
                            data=data,
                            headers={
                                'Authorization': f'Bearer {api_key}'
                            }
                        )

                        if response.status_code == 201:
                            result = response.json()
                            model_id = result.get('_id')
                            model_title = result.get('title')

                            self.log_message.emit(f"✓ {filename} 上传成功，模型ID: {model_id}")
                            uploaded_files.append(filename)

                            # 添加到本地数据
                            voice_model = {
                                'name': model_title or os.path.splitext(filename)[0],
                                'modelId': model_id,
                                'url': f"https://fish.audio/model/{model_id}",
                                'extractTime': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                'type': result.get('type', 'tts'),
                                'description': result.get('description', ''),
                                'author': result.get('author', {}).get('nickname', ''),
                                'state': result.get('state', ''),
                                'visibility': result.get('visibility', ''),
                                'created_at': result.get('created_at', ''),
                                'like_count': result.get('like_count', 0),
                                'task_count': result.get('task_count', 0)
                            }

                            self.voice_models.append(voice_model)

                        else:
                            self.log_message.emit(f"✗ {filename} 上传失败，状态码: {response.status_code}")
                            if response.text:
                                self.log_message.emit(f"错误信息: {response.text}")

                    except Exception as upload_error:
                        self.log_message.emit(f"✗ {filename} 上传失败: {str(upload_error)}")

                    finally:
                        # 确保文件被关闭
                        for _, file_obj in files:
                            if hasattr(file_obj, 'close'):
                                file_obj.close()

                    # 更新进度
                    progress = int(((i + 1) / len(audio_files)) * 100)
                    self.progress_updated.emit(progress)

                    # 添加延迟避免请求过快
                    await asyncio.sleep(1)

            # 保存数据并移动文件
            if uploaded_files:
                self._save_voice_data()
                self.move_uploaded_files(uploaded_files)
                self.table_updated.emit()

            self.log_message.emit(f"上传完成！成功上传 {len(uploaded_files)} 个文件")
            self.fetch_completed.emit(len(uploaded_files))

        except Exception as e:
            self.log_message.emit(f"上传过程失败: {str(e)}")
        finally:
            self.is_fetching = False
            self.progress_updated.emit(100)

    def upload_audio_files(self, api_key: str, audio_files: List[Dict]):
        """同步方法，用于从UI调用"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(self.upload_audio_files_async(api_key, audio_files))
        finally:
            loop.close()
    
    def sync_to_online_table(self):
        """同步声音ID到百度线上表格（待实现）"""
        self.log_message.emit("ID同步功能将在后续版本中实现")
        # TODO: 实现百度表格同步功能
    
    def clear_voice_data(self):
        """清空声音数据"""
        self.voice_models = []
        self._save_voice_data()
        self.table_updated.emit()
        self.log_message.emit("已清空所有声音数据")
    
    def export_voice_data(self, file_path: str):
        """导出声音数据到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.voice_models, f, ensure_ascii=False, indent=2)
            self.log_message.emit(f"声音数据已导出到: {file_path}")
            return True
        except Exception as e:
            self.log_message.emit(f"导出失败: {str(e)}")
            return False
