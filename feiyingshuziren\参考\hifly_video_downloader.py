import os
import asyncio
from datetime import datetime
import pandas as pd
import json
import re
import argparse
from playwright.async_api import async_playwright

# 检查必要的依赖库
def check_dependencies():
    """检查程序所需的依赖库是否已安装"""
    missing_libs = []
    
    # 检查Excel处理相关的库
    try:
        import openpyxl
        print(f"检测到openpyxl版本: {openpyxl.__version__}")
    except ImportError:
        missing_libs.append("openpyxl")
    
    try:
        import xlrd
        print(f"检测到xlrd版本: {xlrd.__version__}")
    except ImportError:
        missing_libs.append("xlrd")
    
    # 如果有缺失的库，打印安装提示
    if missing_libs:
        print("警告: 检测到以下依赖库缺失:")
        for lib in missing_libs:
            print(f"  - {lib}")
        print("\n请运行以下命令安装所有缺失的依赖库:")
        print(f"pip install {' '.join(missing_libs)}")
        print("\n您可以继续运行程序，但可能会在读取Excel文件时遇到问题。")
        return False
    return True

# ===== 用户可配置项 =====
# 认证文件路径
AUTH_FILE = "essential_auth_data.json"  # 存储cookies和localStorage的文件
# 下载目录将动态设置为CSV文件所在目录
DOWNLOAD_DIR = None  # 将在运行时动态设置
# 是否启用截图(可能影响性能)
ENABLE_SCREENSHOTS = False  # 设置为True启用所有截图，False禁用非关键截图
# 并发下载的浏览器实例数
CONCURRENT_BROWSERS = 3  # 默认同时运行2个浏览器，可根据电脑性能调整
# =======================

# 解析命令行参数
def parse_args():
    parser = argparse.ArgumentParser(description="HiFly视频下载工具")
    parser.add_argument('--headless', action='store_true', help='以无头模式运行浏览器（无界面）')
    parser.add_argument('--concurrent', type=int, help='并发下载的浏览器数量（默认为2）')
    return parser.parse_args()

# 加载认证数据
def load_auth_data(auth_file):
    """从文件加载认证数据"""
    if not os.path.exists(auth_file):
        print(f"警告: 认证数据文件 {auth_file} 不存在!")
        print("将创建新的认证数据文件")
        return {"cookies": [], "localStorage": {}}

    try:
        with open(auth_file, "r", encoding="utf-8") as f:
            auth_data = json.load(f)

        cookies = auth_data.get("cookies", [])
        local_storage = auth_data.get("localStorage", {})

        print(f"已加载 {len(cookies)} 个cookie, {len(local_storage)} 个localStorage项")
        return auth_data
    except Exception as e:
        print(f"加载认证数据出错: {e}")
        print("将创建新的认证数据文件")
        return {"cookies": [], "localStorage": {}}

# 保存认证数据
def save_auth_data(auth_file, auth_data):
    """保存认证数据到文件"""
    try:
        with open(auth_file, "w", encoding="utf-8") as f:
            json.dump(auth_data, f, ensure_ascii=False, indent=4)
        print(f"已保存认证数据到 {auth_file}")
        return True
    except Exception as e:
        print(f"保存认证数据出错: {e}")
        return False

# 检查是否已登录
async def check_login_status(page):
    """检查是否已登录"""
    try:
        # 检查是否有登录按钮或其他未登录状态的标志
        login_button = await page.query_selector('button:has-text("登录")')
        if login_button:
            return False

        # 检查是否有已登录状态的标志（例如用户头像或用户名）
        user_avatar = await page.query_selector('.user-avatar, .user-name')
        if user_avatar:
            return True

        # 如果没有明确的登录/未登录标志，尝试检查页面内容
        content = await page.content()
        if '登录' in content and '注册' in content and '忘记密码' in content:
            return False

        # 默认假设已登录
        return True
    except Exception as e:
        print(f"检查登录状态时出错: {e}")
        return False

def find_latest_task_folder():
    """查找最新的创作任务文件夹"""
    task_folders = []

    # 查找所有创作任务文件夹
    for item in os.listdir():
        if os.path.isdir(item) and item.startswith("创作任务_"):
            try:
                # 提取日期部分
                date_str = item.replace("创作任务_", "")
                date_obj = datetime.strptime(date_str, "%Y%m%d")
                task_folders.append((item, date_obj))
            except ValueError:
                continue

    # 如果找到了任务文件夹，返回最新的一个
    if task_folders:
        latest_folder = max(task_folders, key=lambda x: x[1])[0]
        print(f"找到最新的创作任务文件夹: {latest_folder}")
        return latest_folder
    else:
        print("未找到创作任务文件夹")
        return None

def find_excel_file(folder):
    """在指定文件夹中查找生成结果记录.xlsx文件"""
    excel_path = os.path.join(folder, "生成结果记录.xlsx")
    if os.path.exists(excel_path):
        print(f"找到Excel文件: {excel_path}")
        return excel_path
    else:
        # 尝试查找CSV文件作为备选
        csv_path = os.path.join(folder, "生成结果记录.csv")
        if os.path.exists(csv_path):
            print(f"找到CSV文件: {csv_path}")
            return csv_path
        else:
            print(f"在文件夹 {folder} 中未找到生成结果记录.xlsx或.csv文件")
            return None

def read_video_names(file_path):
    """从Excel或CSV文件中读取视频名称、生成模式和下载状态"""
    try:
        # 根据文件扩展名决定读取方式
        if file_path.lower().endswith('.xlsx'):
            # 检查是否安装了openpyxl
            try:
                import openpyxl
                print(f"检测到openpyxl版本: {openpyxl.__version__}")
            except ImportError:
                print("错误: 缺少读取Excel文件所需的openpyxl库")
                print("请运行以下命令安装: pip install openpyxl")
                return [], None
                
            # 尝试多种方式读取Excel文件
            excel_read_success = False
            excel_error_msgs = []
            
            # 方法1: 使用openpyxl引擎
            try:
                df = pd.read_excel(file_path, engine='openpyxl')
                print(f"已使用openpyxl引擎读取Excel文件: {file_path}")
                excel_read_success = True
            except Exception as e:
                excel_error_msgs.append(f"openpyxl引擎失败: {str(e)}")
                print(f"使用openpyxl读取Excel文件失败: {e}")
                print("尝试其他引擎...")
            
            # 方法2: 使用xlrd引擎
            if not excel_read_success:
                try:
                    df = pd.read_excel(file_path, engine='xlrd')
                    print(f"已使用xlrd引擎读取Excel文件: {file_path}")
                    excel_read_success = True
                except Exception as e:
                    excel_error_msgs.append(f"xlrd引擎失败: {str(e)}")
                    print(f"使用xlrd读取Excel文件失败: {e}")
            
            # 方法3: 尝试修复可能损坏的Excel文件（先复制一份）
            if not excel_read_success:
                import shutil
                import tempfile
                try:
                    print("尝试修复可能损坏的Excel文件...")
                    # 创建临时文件来处理可能损坏的Excel
                    temp_dir = tempfile.mkdtemp()
                    fixed_file = os.path.join(temp_dir, "fixed_excel.xlsx")
                    
                    # 复制文件到临时目录
                    shutil.copy2(file_path, fixed_file)
                    
                    # 尝试用较宽松的方式读取
                    try:
                        from zipfile import ZipFile, ZIP_DEFLATED
                        # 尝试读取并重新保存文件
                        try:
                            zin = ZipFile(file_path, 'r')
                            zout = ZipFile(fixed_file, 'w', ZIP_DEFLATED)
                            for item in zin.infolist():
                                data = zin.read(item.filename)
                                zout.writestr(item, data)
                            zout.close()
                            zin.close()
                            print("已尝试修复Excel文件")
                        except Exception as e:
                            print(f"修复Excel文件过程中出错: {e}")
                    except ImportError:
                        print("缺少zipfile模块，无法修复Excel文件")
                    
                    # 尝试读取修复后的文件
                    try:
                        df = pd.read_excel(fixed_file, engine='openpyxl')
                        print(f"已成功读取修复后的Excel文件!")
                        excel_read_success = True
                    except Exception as e:
                        excel_error_msgs.append(f"修复后文件读取失败: {str(e)}")
                        print(f"读取修复后的Excel文件失败: {e}")
                        
                    # 清理临时文件
                    try:
                        shutil.rmtree(temp_dir)
                    except:
                        pass
                except Exception as fix_error:
                    print(f"尝试修复Excel文件时出错: {fix_error}")
            
            # 方法4: 尝试查找同名的CSV文件
            if not excel_read_success:
                csv_path = file_path.replace('.xlsx', '.csv')
                if os.path.exists(csv_path):
                    print(f"检测到同名CSV文件: {csv_path}，尝试读取...")
                    try:
                        df = pd.read_csv(csv_path, encoding='utf-8-sig')
                        print(f"已成功读取同名CSV文件: {csv_path}")
                        excel_read_success = True
                    except Exception as csv_error:
                        excel_error_msgs.append(f"同名CSV读取失败: {str(csv_error)}")
                        print(f"读取同名CSV文件失败: {csv_error}")
            
            # 如果所有方法都失败，创建空的CSV文件并提示用户
            if not excel_read_success:
                print("\n所有读取方法均失败，错误信息如下:")
                for i, msg in enumerate(excel_error_msgs):
                    print(f"  {i+1}. {msg}")
                
                print("\n建议操作:")
                print("1. 请手动打开Excel文件，并另存为CSV格式")
                print("2. 将CSV文件保存为'生成结果记录.csv'，并放在同一文件夹")
                print("3. 再次运行程序\n")
                
                # 创建最小可用的CSV文件
                csv_path = file_path.replace('.xlsx', '.csv')
                print(f"创建最小可用的CSV文件: {csv_path}")
                try:
                    minimal_df = pd.DataFrame({
                        '名称': [],
                        '生成模式': [],
                        '完成状态': [],
                        '完成日期': []
                    })
                    minimal_df.to_csv(csv_path, index=False, encoding='utf-8-sig')
                    print(f"已创建最小可用的CSV文件，请手动填充内容后再次运行程序")
                except Exception as e:
                    print(f"创建CSV文件时出错: {e}")
                
                return [], None
        else:
            # 读取CSV文件
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            print(f"已读取CSV文件: {file_path}")

        if '名称' not in df.columns:
            print("文件中没有'名称'列")
            return [], df

        # 检查是否有生成模式列
        if '生成模式' not in df.columns:
            print("CSV文件中没有'生成模式'列，将所有视频当作积分模式处理")

        # 检查是否有完成状态列，如果没有则添加
        if '完成状态' not in df.columns:
            # 如果有旧版的下载状态列，将其重命名为完成状态列
            if '下载状态' in df.columns:
                print("CSV文件中有'下载状态'列，将其重命名为'完成状态'列")
                df['完成状态'] = df['下载状态']
                df.drop('下载状态', axis=1, inplace=True)
            else:
                print("CSV文件中没有'完成状态'列，添加该列")
                df['完成状态'] = ''

        # 检查是否有完成日期列，如果没有则添加
        if '完成日期' not in df.columns:
            print("CSV文件中没有'完成日期'列，添加该列")
            df['完成日期'] = ''

        # 创建包含名称、模式和完成状态的列表
        video_info = []
        for index, row in df.iterrows():
            name = row['名称']
            # 判断模式，如果包含"暗黑"则为暗黑模式，否则为积分模式
            mode = '暗黑' if '生成模式' in df.columns and '暗黑' in str(row.get('生成模式', '')) else '积分'
            # 获取完成状态
            status = row.get('完成状态', '')

            # 只添加下载状态为空或"下载失败"的视频
            if status != '已交付':
                video_info.append({'name': name, 'mode': mode, 'index': index})

        # 按模式对视频进行排序，优先下载积分模式的视频
        # 将视频分为积分和暗黑两组
        points_videos = [info for info in video_info if info['mode'] == '积分']
        dark_videos = [info for info in video_info if info['mode'] == '暗黑']

        # 先添加积分模式的视频，再添加暗黑模式的视频
        sorted_video_info = points_videos + dark_videos

        # 统计信息
        total_videos = len(df)
        to_download = len(sorted_video_info)
        already_downloaded = sum(1 for status in df['完成状态'] if status == '已交付')

        print(f"共有 {total_videos} 个视频，其中 {already_downloaded} 个已交付，{to_download} 个需要下载")

        # 统计不同模式的视频数量
        if to_download > 0:
            points_count = len(points_videos)
            dark_count = len(dark_videos)
            print(f"其中需要下载的视频中，积分模式: {points_count} 个, 暗黑模式: {dark_count} 个")
            print(f"将优先下载积分模式的视频，然后再下载暗黑模式的视频")

        return sorted_video_info, df
    except ImportError as ie:
        print(f"缺少必要的库: {ie}")
        print("请安装必要的依赖库: pip install pandas openpyxl xlrd")
        return [], None
    except Exception as e:
        print(f"读取文件出错: {e}")
        import traceback
        traceback.print_exc()  # 打印详细错误堆栈
        return [], None

async def safe_screenshot(page, filename):
    """安全地截图，不会因为截图失败而中断程序"""
    if not ENABLE_SCREENSHOTS:
        return

    try:
        await page.screenshot(path=filename)
        print(f"已保存截图: {filename}")
    except Exception as e:
        print(f"截图失败: {e}")

async def set_page_size(page, size=100):
    """设置每页显示的条目数"""
    try:
        print(f"设置每页显示 {size} 条...")

        # 根据您提供的前端代码，首先需要点击选择器打开下拉菜单
        # 1. 点击选择器打开下拉菜单
        print("点击选择器打开下拉菜单...")
        await page.click('.ant-pagination-options-size-changer')
        await page.wait_for_timeout(1000)  # 等待下拉菜单出现

        # 2. 选择对应的选项
        print(f"选择每页 {size} 条选项...")
        await page.click(f'.ant-select-item[title="{size} 条/页"]')
        await page.wait_for_timeout(2000)  # 等待页面刷新

        # 3. 验证设置是否成功
        page_size_text = await page.evaluate('''
            () => {
                const sizeChanger = document.querySelector('.ant-pagination-options-size-changer .ant-select-selection-item');
                return sizeChanger ? sizeChanger.textContent : null;
            }
        ''')

        if page_size_text and str(size) in page_size_text:
            print(f"✓ 成功设置每页显示 {size} 条")
            return True
        else:
            print(f"无法设置页面大小，当前显示: {page_size_text}")
            print("将继续使用当前页面大小进行操作")
            return False
    except Exception as e:
        print(f"设置页码大小时出错: {e}")
        print("将继续使用当前页面大小进行操作")
        return False

async def check_and_close_ad_popup(page):
    """检查并关闭可能出现的广告弹窗"""
    try:
        print("检查页面是否存在广告弹窗...")
        
        # 使用原始代码中的实现：仅查找特定选择器并点击
        close_button = await page.query_selector('.activity-modal-content .ant-modal-close')
        if close_button:
            # 检查关闭按钮是否可见
            is_visible = await close_button.is_visible()
            if is_visible:
                print("检测到广告弹窗，点击关闭按钮...")
                # 设置短超时时间
                try:
                    await close_button.click(timeout=5000)
                    print("已关闭广告弹窗")
                except Exception as click_error:
                    print(f"点击关闭按钮失败: {click_error}")
                    print("尝试按ESC键关闭...")
                    await page.keyboard.press("Escape")
            else:
                print("检测到广告弹窗，但关闭按钮不可见")
                print("尝试按ESC键关闭...")
                await page.keyboard.press("Escape")
            
            # 等待动画完成
            await page.wait_for_timeout(1000)
        else:
            print("未检测到广告弹窗，继续执行")
    except Exception as popup_error:
        print(f"处理广告弹窗时出错: {popup_error}")
        # 继续执行，不中断流程
    
    return True

async def find_and_download_video(page, video_name, set_current_video_func=None):
    """查找并下载指定名称的视频

    Args:
        page: 浏览器页面
        video_name: 要下载的视频名称
        set_current_video_func: 设置当前下载视频的回调函数
    """
    try:
        # Import re module inside the function to avoid scoping issues
        import re
        
        print(f"DEBUG: 开始处理视频 {video_name}")
        # 如果传入的是视频信息对象，则提取名称
        video_info = {'name': video_name, 'mode': '积分'}
        if isinstance(video_name, dict) and 'name' in video_name:
            video_info = video_name
            video_name = video_info['name']

        # 设置当前正在下载的视频信息
        if set_current_video_func:
            set_current_video_func(video_info)

        print(f"\n查找视频: {video_name}")

        # 添加: 检查并关闭广告弹窗
        await check_and_close_ad_popup(page)

        # 提取名称中第一个连续的6位数进行匹配（视频ID）
        id_match = re.search(r'\d{6}', video_name)
        id_part = None
        if id_match:
            id_part = id_match.group(0)
            print(f"提取到视频ID: {id_part}")

        # 等待页面完全加载
        await page.wait_for_timeout(2000)  # 增加等待时间确保页面完全加载

        # 获取页面上所有卡片的标题和对应的卡片元素
        card_titles = await page.evaluate('''
            () => {
                const titles = [];
                const allBoxTitles = document.querySelectorAll('.box-title');
                const allListBoxes = Array.from(document.querySelectorAll('.list-box'));

                console.log(`找到 ${allBoxTitles.length} 个卡片标题和 ${allListBoxes.length} 个卡片容器`);

                allBoxTitles.forEach((el, index) => {
                    const listBox = el.closest('.list-box');
                    const listBoxIndex = listBox ? allListBoxes.indexOf(listBox) : -1;

                    // 添加更多调试信息
                    console.log(`卡片标题 ${index}: ${el.textContent.trim()}, 容器索引: ${listBoxIndex}`);

                    titles.push({
                        text: el.textContent.trim(),
                        id: listBoxIndex,
                        index: index  // 添加原始索引作为备用
                    });
                });
                return titles;
            }
        ''')

        print(f"页面上找到 {len(card_titles)} 个视频卡片")
        if len(card_titles) > 0:
            print("页面上的卡片标题示例:")
            for i, card in enumerate(card_titles[:3]):
                print(f"  {i+1}. {card['text']}")
            if len(card_titles) > 3:
                print(f"  ... 及其他 {len(card_titles)-3} 个卡片")

        # 尝试匹配视频
        matched_card_id = -1
        matched_by = ""
        
        # 提取视频名称中的素材ID（从后往前找第一个独立的5位数）
        material_id = None
        # 使用正则表达式匹配独立的5位数（前后不能是数字）
        independent_5digit_matches = re.findall(r'(?<!\d)\d{5}(?!\d)', video_name)
        
        if independent_5digit_matches:
            # 使用最后一个独立的5位数作为素材ID
            material_id = independent_5digit_matches[-1]
            print(f"提取到素材ID: {material_id} (从后往前第一个独立的5位数)")
        else:
            print(f"在视频名称中未找到独立的5位数作为素材ID")

        # 添加精确匹配完整视频名称的方法（最高优先级）
        exact_name_match = False
        for i, card in enumerate(card_titles):
            card_text = card['text']
            # 完全精确匹配完整名称（最高优先级）
            if card_text == video_name:
                matched_card_id = card['id'] if card['id'] >= 0 else card['index']
                matched_by = "完全精确匹配视频名称"
                exact_name_match = True
                print(f"完全精确匹配到卡片: {card_text}, 卡片ID: {matched_card_id}")
                break
        
        # 如果没有精确匹配完整名称，尝试同时匹配视频ID和素材ID
        if not exact_name_match and id_part and material_id:
            print(f"尝试匹配视频ID:{id_part}和素材ID:{material_id}")
            best_match_id = -1
            best_match_card = None
            
            for i, card in enumerate(card_titles):
                card_text = card['text']
                # 检查是否同时包含视频ID和素材ID
                if id_part in card_text and material_id in card_text:
                    best_match_id = card['id'] if card['id'] >= 0 else card['index']
                    best_match_card = card
                    matched_card_id = best_match_id
                    matched_by = f"视频ID和素材ID精确匹配"
                    print(f"ID+素材ID匹配到卡片: {card_text}, 卡片ID: {matched_card_id}")
                    break
            
            if best_match_id == -1:
                print(f"未找到同时包含视频ID:{id_part}和素材ID:{material_id}的卡片")
        
        # 如果上面的方法都没有匹配到，尝试仅使用视频ID进行匹配
        if matched_card_id == -1 and id_part:
            for i, card in enumerate(card_titles):
                card_text = card['text']
                if id_part in card_text:
                    # 使用卡片的ID，如果ID是-1，则使用原始索引
                    matched_card_id = card['id'] if card['id'] >= 0 else card['index']
                    matched_by = f"仅视频ID匹配"
                    print(f"使用视频ID {id_part} 匹配到卡片: {card_text}, 卡片ID: {matched_card_id}")
                    break

        # 如果找到了匹配的卡片，检查暗黑模式视频是否在生成中
        if matched_card_id >= 0 and video_info['mode'] == '暗黑':
            # 检查具体匹配到的卡片是否有"生成中"的提示
            print(f"检查暗黑模式视频是否在生成中: {video_name}")
            # 获取实际匹配到的卡片元素
            cards = await page.query_selector_all('.list-box')
            if 0 <= matched_card_id < len(cards):
                target_card = cards[matched_card_id]
                
                # 检查该卡片是否有生成中的提示
                has_generating = await page.evaluate("""
                    (cardElement) => {
                        try {
                            // 检查卡片内是否有生成中的提示文本
                            const hasGeneratingText = cardElement.textContent.includes('生成中');
                            
                            // 检查是否有loading图标
                            const hasLoadingIcon = cardElement.querySelector('.anticon-loading.anticon-spin') !== null;
                            
                            // 检查是否有pending或mask类
                            const hasPendingClass = cardElement.querySelector('.pending') !== null;
                            const hasMaskClass = cardElement.querySelector('.mask') !== null;
                            
                            // 更精确地检查"生成中, 请稍等"文本
                            const pendingElement = cardElement.querySelector('.pending');
                            const hasPendingText = pendingElement ? 
                                (pendingElement.textContent.includes('生成中') || 
                                 pendingElement.textContent.includes('请稍等')) : false;
                            
                            // 检查更具体的结构：.mask .pending 元素
                            const hasMaskPending = cardElement.querySelector('.mask .pending') !== null;
                            
                            console.log('检查生成中状态:', {
                                hasGeneratingText,
                                hasLoadingIcon,
                                hasPendingClass,
                                hasMaskClass,
                                hasPendingText,
                                hasMaskPending
                            });
                            
                            // 如果有标准组合标志，肯定是生成中
                            if (hasMaskPending && hasLoadingIcon) {
                                console.log('检测到标准的"生成中"指示器');
                                return true;
                            }
                            
                            // 如果有任一生成中的文本提示，也认为是生成中
                            if (hasPendingText || hasGeneratingText) {
                                console.log('检测到"生成中"文本');
                                return true;
                            }
                            
                            // 如果同时有pending类和loading图标，也视为生成中
                            if (hasPendingClass && hasLoadingIcon) {
                                console.log('检测到pending类和loading图标');
                                return true;
                            }
                            
                            return false;
                        } catch (e) {
                            console.error('检查生成中状态出错:', e);
                            return false;
                        }
                    }
                """, target_card)
                
                if has_generating:
                    print(f"检测到暗黑模式视频 '{video_name}' 有生成中的提示")
                    print(f"跳过此视频并标记为下载失败")
                    # 返回特殊状态码，表示生成中的视频
                    return 'GENERATING'
                else:
                    print(f"暗黑模式视频 '{video_name}' 无生成中状态，继续处理")
        
        # 如果找到匹配的卡片，使用JavaScript点击它
        if matched_card_id >= 0:  # 使用 >= 0 而不是 != -1，以保持一致性
            print(f"✓ 成功匹配到卡片 (方式: {matched_by})")
            print(f"DEBUG: 卡片ID为 {matched_card_id}")

            # 使用JavaScript点击匹配到的卡片上的下载按钮
            download_clicked = False
            try:
                # 滚动到卡片位置并点击下载按钮
                js_result = await page.evaluate(f'''
                    (cardIndex) => {{
                        // 获取所有卡片
                        const cards = Array.from(document.querySelectorAll('.list-box'));
                        if (cardIndex >= 0 && cardIndex < cards.length) {{
                            const targetCard = cards[cardIndex];

                            // 滚动到卡片位置
                            targetCard.scrollIntoView({{ behavior: 'smooth', block: 'center' }});

                            // 等待一下滚动完成并悬停在卡片上
                            setTimeout(() => {{
                                // 悬停在卡片上
                                const mouseoverEvent = new MouseEvent('mouseover', {{
                                    bubbles: true,
                                    cancelable: true,
                                    view: window
                                }});
                                targetCard.dispatchEvent(mouseoverEvent);
                            }}, 500);

                            // 尝试找到并点击下载按钮
                            const downloadBtn = targetCard.querySelector('.op .btn .anticon-download');
                            if (downloadBtn) {{
                                const btn = downloadBtn.closest('.btn');
                                if (btn) {{
                                    console.log('找到并点击特定卡片的下载按钮');
                                    // 等待一下再点击
                                    setTimeout(() => {{
                                        btn.click();
                                    }}, 1000);
                                    return true;
                                }}
                            }}

                            // 如果没有找到下载按钮，尝试点击任何操作按钮
                            const opBtn = targetCard.querySelector('.op .btn');
                            if (opBtn) {{
                                console.log('找到并点击特定卡片的操作按钮');
                                setTimeout(() => {{
                                    opBtn.click();
                                }}, 1000);
                                return true;
                            }}

                            return false;
                        }}
                        return false;
                    }}
                ''', matched_card_id)

                if js_result:
                    print("✓ 通过JavaScript成功点击卡片上的按钮")
                    download_clicked = True
                    # 等待下载开始
                    await page.wait_for_timeout(3000)
                    return True
                else:
                    print("通过JavaScript无法点击卡片上的按钮，尝试其他方法")
            except Exception as e:
                print(f"JavaScript点击失败: {e}")

            # 如果上面的方法失败，尝试使用Playwright的方法
            if not download_clicked:
                try:
                    # 获取所有卡片
                    cards = await page.query_selector_all('.list-box')
                    print(f"DEBUG: 找到 {len(cards)} 个卡片容器，matched_card_id = {matched_card_id}")
                    if 0 <= matched_card_id < len(cards):
                        target_card = cards[matched_card_id]
                        print(f"DEBUG: 成功获取目标卡片")

                        # 滚动到卡片位置
                        await target_card.scroll_into_view_if_needed()
                        await page.wait_for_timeout(1000)  # 等待滚动完成

                        # 悬停在卡片上
                        await target_card.hover()
                        await page.wait_for_timeout(1000)  # 等待下载按钮出现

                        # 尝试点击下载按钮
                        download_btn = await target_card.query_selector('.op .btn .anticon-download')
                        if download_btn:
                            await download_btn.click()
                            print("✓ 成功点击卡片上的下载按钮")
                            download_clicked = True
                            # 等待下载开始
                            await page.wait_for_timeout(2000)
                            return True
                        else:
                            # 如果没有找到下载按钮，尝试点击任何操作按钮
                            op_btn = await target_card.query_selector('.op .btn')
                            if op_btn:
                                await op_btn.click()
                                print("✓ 成功点击卡片上的操作按钮")
                                download_clicked = True
                                # 等待下载开始
                                await page.wait_for_timeout(2000)
                                return True
                except Exception as e:
                    print(f"Playwright点击失败: {e}")

        # 打印匹配结果的调试信息
        print(f"DEBUG: 匹配结果 - matched_card_id: {matched_card_id}, matched_by: {matched_by}")

        # 直接使用更可靠的方法查找和点击卡片
        print(f"DEBUG: 尝试直接使用卡片的名称进行点击")
        try:
            # 查找包含视频名称的卡片标题
            card_selector = None
            if matched_by == "完全精确匹配视频名称":
                card_selector = f".box-title:text-is('{video_name}')"
                print(f"DEBUG: 使用精确文本选择器: '{card_selector}'")
            elif matched_by == "视频ID和素材ID精确匹配" or matched_by == "仅视频ID匹配":
                card_selector = f".box-title:has-text('{id_part}')"
                print(f"DEBUG: 使用ID包含选择器: '{card_selector}'")
            else:
                card_selector = f".box-title:has-text('{video_name}')"
                print(f"DEBUG: 使用名称包含选择器: '{card_selector}'")
                
            print(f"DEBUG: 使用选择器 '{card_selector}' 查找卡片")

            card_title = await page.query_selector(card_selector)
            if card_title:
                print(f"DEBUG: 找到卡片标题")
                # 获取卡片标题的文本
                title_text = await card_title.text_content()
                print(f"DEBUG: 卡片标题文本: {title_text}")

                # 如果是暗黑模式视频，检查是否在生成中
                if video_info['mode'] == '暗黑':
                    # 获取卡片容器
                    card_container = await card_title.evaluate('el => el.closest(".list-box")')
                    if card_container:
                        has_generating = await page.evaluate("""
                            (cardElement) => {
                                try {
                                    // 检查卡片内是否有生成中的提示
                                    const hasGeneratingText = cardElement.textContent.includes('生成中');
                                    
                                    // 检查是否有loading图标
                                    const hasLoadingIcon = cardElement.querySelector('.anticon-loading.anticon-spin') !== null;
                                    
                                    // 检查是否有pending或mask类
                                    const hasPendingClass = cardElement.querySelector('.pending') !== null;
                                    const hasMaskClass = cardElement.querySelector('.mask') !== null;
                                    
                                    // 如果有任一指示符，认为视频在生成中
                                    return hasGeneratingText || hasLoadingIcon || hasPendingClass || hasMaskClass;
                                } catch (e) {
                                    console.error('检查生成中状态出错:', e);
                                    return false;
                                }
                            }
                        """, card_container)
                        
                        if has_generating:
                            print(f"检测到暗黑模式视频 '{video_name}' 有生成中的提示")
                            print(f"跳过此视频并标记为下载失败")
                            # 返回特殊状态码，表示生成中的视频
                            return 'GENERATING'
                        else:
                            print(f"暗黑模式视频 '{video_name}' 无生成中状态，继续处理")

                # 滚动到卡片位置
                await card_title.scroll_into_view_if_needed()
                await page.wait_for_timeout(2000)

                # 悬停在卡片上
                await card_title.hover()
                await page.wait_for_timeout(2000)

                # 尝试点击下载按钮
                download_btn = await page.query_selector(f"{card_selector} >> xpath=../.. >> .anticon-download")
                if download_btn:
                    print(f"DEBUG: 找到下载按钮，准备点击")
                    await download_btn.click()
                    print(f"DEBUG: 已点击下载按钮")
                    await page.wait_for_timeout(3000)
                    return True
                else:
                    print(f"DEBUG: 未找到下载按钮，尝试点击任何操作按钮")
                    # 尝试点击任何操作按钮
                    op_btn = await page.query_selector(f"{card_selector} >> xpath=../.. >> .op .btn")
                    if op_btn:
                        print(f"DEBUG: 找到操作按钮，准备点击")
                        await op_btn.click()
                        print(f"DEBUG: 已点击操作按钮")
                        await page.wait_for_timeout(3000)
                        return True
                    else:
                        print(f"DEBUG: 未找到任何可点击的按钮")
            else:
                print(f"DEBUG: 未找到卡片标题")
        except Exception as e:
            print(f"DEBUG: 直接方法查找卡片失败: {e}")

        # 如果上面的方法都失败，尝试使用更简单的方法
        print(f"DEBUG: 尝试使用更简单的方法")
        try:
            # 查找可能匹配的下载按钮 - 但需要验证内容
            download_btns = await page.query_selector_all('.anticon-download')
            print(f"DEBUG: 找到 {len(download_btns)} 个下载按钮")

            if download_btns and len(download_btns) > 0:
                # 不再直接点击第一个下载按钮
                # 修改: 如果视频名称中包含视频ID，则需要验证点击正确的视频
                
                # 提取视频ID
                id_match = re.search(r'\d{6}', video_name)
                
                if id_match:
                    video_id = id_match.group(0)
                    print(f"DEBUG: 需要验证视频ID: {video_id} 匹配才能下载")
                    
                    # 错误修复: 不应下载无法匹配ID的视频
                    print(f"⚠️ 未找到匹配ID为 {video_id} 的视频: {video_name}")
                    print(f"为防止下载错误视频，放弃下载")
                    return False
                else:
                    # 如果视频名称没有ID，则可能是老文件，需要特殊处理
                    print(f"⚠️ 视频名称 {video_name} 中未找到视频ID，无法安全匹配")
                    print(f"为防止下载错误视频，放弃下载")
                    return False
        except Exception as e:
            print(f"DEBUG: 简单方法失败: {e}")

        # 如果所有方法都失败，返回失败
        print(f"⚠️ 未找到匹配的视频卡片: {video_name}")
        print(f"为防止下载错误视频，不使用原始方法点击下载按钮")
        return False
    except Exception as e:
        print(f"查找视频时出错: {e}")
        return False

def update_download_status(df, file_path, video_info, status):
    """更新Excel或CSV文件中的完成状态

    Args:
        df: 数据帧
        file_path: 文件路径（Excel或CSV）
        video_info: 视频信息对象
        status: 要设置的状态（已交付或下载失败）
    """
    try:
        # 更新数据帧中的状态
        df.at[video_info['index'], '完成状态'] = status

        # 如果状态是已交付，添加完成日期
        if status == '已交付' and '完成日期' in df.columns:
            # 获取当前日期并格式化为"X月X日"
            from datetime import datetime
            today = datetime.now()
            date_str = f"{today.month}月{today.day}日"
            df.at[video_info['index'], '完成日期'] = date_str

        # 不再更新姓名列，因为原始表格中已经有了

        # 调整列顺序，将工单号、完成状态和完成日期放到最后
        if '工单号' in df.columns and '完成状态' in df.columns and '完成日期' in df.columns:
            # 获取所有列名
            cols = list(df.columns)
            
            # 移除工单号、完成状态和完成日期
            if '工单号' in cols: cols.remove('工单号')
            if '完成状态' in cols: cols.remove('完成状态')
            if '完成日期' in cols: cols.remove('完成日期')
            
            # 将工单号、完成状态和完成日期添加到列表末尾
            cols.append('工单号')
            cols.append('完成状态')
            cols.append('完成日期')
            
            # 重新排序DataFrame
            df = df[cols]

        # 根据文件扩展名决定保存方式
        if file_path.lower().endswith('.xlsx'):
            # 保存到Excel文件
            df.to_excel(file_path, index=False)
            print(f"已更新视频 '{video_info['name']}' 的完成状态为: {status} (保存到Excel)")
        else:
            # 保存到CSV文件
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
            print(f"已更新视频 '{video_info['name']}' 的完成状态为: {status} (保存到CSV)")
        return True
    except Exception as e:
        print(f"更新下载状态时出错: {e}")
        return False

def check_downloaded_files(df, file_path, points_dir, dark_dir):
    """检查下载目录中的文件，更新之前标记为失败但实际已下载的视频状态

    Args:
        df: 数据帧
        file_path: 文件路径（Excel或CSV）
        points_dir: 积分目录
        dark_dir: 暗黑目录
    """
    try:
        # 获取所有已下载的文件
        downloaded_files = []

        # 检查积分目录
        if os.path.exists(points_dir):
            for file in os.listdir(points_dir):
                if file.endswith('.mp4'):
                    downloaded_files.append(file)

        # 检查暗黑目录
        if os.path.exists(dark_dir):
            for file in os.listdir(dark_dir):
                if file.endswith('.mp4'):
                    downloaded_files.append(file)

        print(f"已检测到 {len(downloaded_files)} 个已下载的视频文件")

        # 获取所有标记为失败的视频
        failed_videos = df[df['完成状态'] == '下载失败']
        updated_count = 0

        # 检查每个失败的视频是否实际已下载
        for index, row in failed_videos.iterrows():
            video_name = row['名称']

            # 提取视频ID
            id_match = re.search(r'\d{6}', video_name)
            if id_match:
                video_id = id_match.group(0)

                # 检查是否有包含该ID的文件
                for file in downloaded_files:
                    if video_id in file:
                        # 找到包含该ID的文件，更新状态
                        video_info = {'name': video_name, 'index': index}
                        # 更新完成状态和完成日期
                        update_download_status(df, file_path, video_info, '已交付')
                        updated_count += 1
                        break

        if updated_count > 0:
            print(f"已更新 {updated_count} 个之前标记为失败但实际已下载的视频状态")

        return updated_count
    except Exception as e:
        print(f"检查已下载文件时出错: {e}")
        return 0

async def check_next_page(page):
    """检查并点击下一页按钮"""
    try:
        # 检查下一页按钮是否存在且可点击
        next_button = await page.query_selector('li.ant-pagination-next:not(.ant-pagination-disabled)')
        if next_button:
            print("点击下一页按钮...")
            await next_button.click()
            # 等待页面加载
            await page.wait_for_timeout(3000)
            return True
        else:
            print("没有下一页或下一页按钮已禁用")
            return False
    except Exception as e:
        print(f"检查下一页时出错: {e}")
        return False

async def process_videos_with_browser(videos_to_download, file_path, df, headless_mode, browser_id=1):
    """使用单个浏览器实例处理一组视频

    Args:
        videos_to_download: 要下载的视频列表
        file_path: 文件路径（Excel或CSV）
        df: 数据帧
        headless_mode: 是否使用无头模式
        browser_id: 浏览器实例ID（用于日志区分）
    """
    print(f"浏览器实例 #{browser_id} 启动，准备处理 {len(videos_to_download)} 个视频")
    
    # 记录下载结果
    download_results = {
        "成功": [],
        "失败": []
    }
    
    # 加载认证数据
    auth_data = load_auth_data(AUTH_FILE)
    if not auth_data:
        print(f"浏览器 #{browser_id}: 无法加载认证数据，程序终止")
        return download_results
    
    # 连接到浏览器
    async with async_playwright() as p:
        # 创建新的浏览器实例
        print(f"浏览器 #{browser_id}: 启动浏览器...")
        browser = await p.chromium.launch(headless=headless_mode)

        # 创建新的上下文
        context = await browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
            accept_downloads=True
        )

        # 设置下载路径
        download_path = os.path.abspath(DOWNLOAD_DIR)
        print(f"浏览器 #{browser_id}: 设置下载路径: {download_path}")

        # 创建页面
        page = await context.new_page()

        # 当前正在下载的视频信息
        current_downloading_video_info = None
        download_detected = False  # 标记是否检测到下载

        # 存储所有视频信息的字典，用于根据ID匹配视频
        video_id_map = {}

        # 处理下载的函数
        async def handle_download(download):
            nonlocal current_downloading_video_info, download_detected, video_id_map
            print(f"浏览器 #{browser_id}: 检测到下载: {download.suggested_filename}")
            download_detected = True  # 设置为已检测到下载

            # 从文件名中提取ID
            id_match = re.search(r'\d{6}', download.suggested_filename)
            matched_video_info = None

            if id_match:
                file_id = id_match.group(0)
                print(f"浏览器 #{browser_id}: 从下载文件名中提取到视频ID: {file_id}")
                
                # 先检查当前下载的视频是否匹配
                if current_downloading_video_info and file_id in current_downloading_video_info['name']:
                    matched_video_info = current_downloading_video_info
                    print(f"浏览器 #{browser_id}: 文件匹配当前下载的视频: {matched_video_info['name']}")
                # 如果不匹配当前视频，尝试从字典中查找
                elif file_id in video_id_map:
                    matched_video_info = video_id_map[file_id]
                    print(f"浏览器 #{browser_id}: 文件匹配到字典中的视频: {matched_video_info['name']}")
                else:
                    print(f"浏览器 #{browser_id}: 警告: 下载的文件ID {file_id} 未匹配到任何已知视频")

            # 使用匹配到的视频信息（如果有），否则使用当前下载的视频信息
            video_info_to_use = matched_video_info or current_downloading_video_info

            # 如果仍然没有视频信息，创建一个基本的信息对象
            if not video_info_to_use:
                print(f"浏览器 #{browser_id}: 警告: 无法匹配到视频信息，使用文件名创建基本信息")
                video_info_to_use = {
                    'name': download.suggested_filename.replace('.mp4', ''),
                    'mode': '积分',  # 默认使用积分模式
                    'index': -1  # 设置一个无效的索引，表示不更新状态
                }

            if video_info_to_use:
                # 根据视频模式选择保存目录
                if video_info_to_use['mode'] == '暗黑':
                    # 暗黑模式保存到暗黑目录
                    save_dir = os.path.join(DOWNLOAD_DIR, "暗黑")
                    print(f"浏览器 #{browser_id}: 暗黑模式视频，保存到暗黑目录")
                else:
                    # 积分模式或未知模式保存到积分目录
                    save_dir = os.path.join(DOWNLOAD_DIR, "积分")
                    print(f"浏览器 #{browser_id}: 积分模式视频，保存到积分目录")

                # 使用原始文件名，不重命名
                save_path = os.path.join(save_dir, download.suggested_filename)
                print(f"浏览器 #{browser_id}: 保存原始文件名: {download.suggested_filename}")

                # 确保目录存在
                os.makedirs(save_dir, exist_ok=True)

                try:
                    # 保存文件
                    await download.save_as(save_path)
                    print(f"浏览器 #{browser_id}: 已保存文件到: {save_path}")

                    # 检查文件是否存在
                    if os.path.exists(save_path):
                        print(f"浏览器 #{browser_id}: 文件存在，大小: {os.path.getsize(save_path)} 字节")
                        # 更新Excel文件中的下载状态为"已交付"
                        # 确保使用正确的视频信息更新状态
                        if video_info_to_use and 'index' in video_info_to_use and video_info_to_use['index'] >= 0:
                            update_download_status(df, file_path, video_info_to_use, '已交付')
                            print(f"浏览器 #{browser_id}: 已更新视频 '{video_info_to_use['name']}' 的状态为已交付")
                        else:
                            print(f"浏览器 #{browser_id}: 警告: 无法更新视频状态，视频信息不完整")
                        print(f"浏览器 #{browser_id}: ✓ 成功下载视频: {video_info_to_use['name']}")

                        # 如果这个视频不是当前正在下载的视频，说明是延迟触发的下载
                        # 需要从全局待下载列表中移除它，防止其他浏览器实例重复下载
                        if matched_video_info and matched_video_info != current_downloading_video_info:
                            # 从待下载列表中移除这个视频，防止它被标记为失败
                            for video in list(videos_to_download):
                                if 'name' in video and matched_video_info['name'] == video['name']:
                                    print(f"浏览器 #{browser_id}: 从待下载列表中移除延迟触发的下载: {video['name']}")
                                    if video in videos_to_download:
                                        videos_to_download.remove(video)
                        
                        # 将视频添加到成功列表
                        if video_info_to_use not in download_results["成功"]:
                            download_results["成功"].append(video_info_to_use)
                    else:
                        print(f"浏览器 #{browser_id}: 警告: 文件保存后不存在: {save_path}")
                except Exception as e:
                    print(f"浏览器 #{browser_id}: 保存文件时出错: {e}")
            else:
                print(f"浏览器 #{browser_id}: 无法确定视频信息，无法保存文件")

        # 注册下载事件处理程序
        page.on("download", lambda download: asyncio.ensure_future(handle_download(download)))

        print(f"浏览器 #{browser_id}: 浏览器页面已准备就绪")

        # 设置较长的超时时间
        page.set_default_timeout(30000)  # 30秒

        try:
            # 导航到主页面
            print(f"浏览器 #{browser_id}: 正在导航到主页面 https://hifly.cc/...")
            await page.goto("https://hifly.cc/", timeout=30000, wait_until="domcontentloaded")
            print(f"浏览器 #{browser_id}: 页面DOM已加载完成")

            # 尝试使用现有cookie和localStorage登录
            cookies = auth_data.get("cookies", [])
            if cookies:
                await context.add_cookies(cookies)
                print(f"浏览器 #{browser_id}: 已应用 {len(cookies)} 个cookie")

            # 应用localStorage (登录的关键步骤)
            local_storage = auth_data.get("localStorage", {})
            if local_storage:
                try:
                    for key, value in local_storage.items():
                        await page.evaluate(f"""() => {{
                            try {{
                                localStorage.setItem('{key}', {json.dumps(value)});
                            }} catch(e) {{
                                console.error('设置localStorage项出错:', e);
                            }}
                        }}""")
                    print(f"浏览器 #{browser_id}: 已应用 {len(local_storage)} 个localStorage项")
                except Exception as e:
                    print(f"浏览器 #{browser_id}: 设置localStorage时出错: {e}")

            # 等待页面加载完成
            await page.wait_for_timeout(3000)

            # 刷新页面以应用设置
            await page.reload()
            await page.wait_for_timeout(3000)

            # 检查是否已登录
            is_logged_in = await check_login_status(page)

            if not is_logged_in:
                print(f"浏览器 #{browser_id}: 检测到未登录状态，请手动登录...")
                print(f"浏览器 #{browser_id}: 请在浏览器中完成登录操作，登录成功后脚本将自动继续")

                # 等待用户手动登录
                login_timeout = 300  # 5分钟超时
                login_start_time = datetime.now()

                while not is_logged_in and (datetime.now() - login_start_time).total_seconds() < login_timeout:
                    # 每5秒检查一次登录状态
                    await page.wait_for_timeout(5000)
                    is_logged_in = await check_login_status(page)
                    if is_logged_in:
                        print(f"浏览器 #{browser_id}: 检测到已登录状态！")
                        break
                    else:
                        print(f"浏览器 #{browser_id}: 等待登录中...")

                if not is_logged_in:
                    print(f"浏览器 #{browser_id}: 登录超时，程序终止")
                    return download_results

                # 登录成功，获取并保存认证数据
                print(f"浏览器 #{browser_id}: 登录成功，获取认证数据...")

                # 获取cookies
                new_cookies = await context.cookies()

                # 获取localStorage
                new_local_storage = await page.evaluate('''
                    () => {
                        const items = {};
                        for (let i = 0; i < localStorage.length; i++) {
                            const key = localStorage.key(i);
                            items[key] = localStorage.getItem(key);
                        }
                        return items;
                    }
                ''')

                # 更新认证数据
                auth_data = {
                    "cookies": new_cookies,
                    "localStorage": new_local_storage
                }

                # 保存认证数据
                save_auth_data(AUTH_FILE, auth_data)
                print(f"浏览器 #{browser_id}: 认证数据已更新并保存")
            else:
                print(f"浏览器 #{browser_id}: 检测到已登录状态，继续执行...")

            # 导航到视频管理页面
            print(f"浏览器 #{browser_id}: 正在导航到视频管理页面...")
            await page.goto("https://hifly.cc/video", timeout=30000, wait_until="domcontentloaded")
            print(f"浏览器 #{browser_id}: 视频管理页面已加载")

            # 等待页面完全加载
            print(f"浏览器 #{browser_id}: 等待页面完全加载...")
            await page.wait_for_timeout(8000)  # 增加等待时间确保页面完全加载
            
            # 在页面完全加载后立即检查广告弹窗
            print(f"浏览器 #{browser_id}: 检查页面是否有广告弹窗...")
            await check_and_close_ad_popup(page)

            # 检查页面上的视频卡片数量
            card_count = await page.evaluate('''
                () => {
                    return document.querySelectorAll('.list-box').length;
                }
            ''')
            print(f"浏览器 #{browser_id}: 页面上找到 {card_count} 个视频卡片")

            # 尝试设置每页显示100条
            try:
                await set_page_size(page, 100)
            except Exception as e:
                print(f"浏览器 #{browser_id}: 设置每页显示条数时出错，将使用默认值: {e}")

            # 处理所有视频 - 只处理第一页
            remaining_videos = videos_to_download.copy()
            
            print(f"\n浏览器 #{browser_id}: 只处理第一页视频，共 {len(remaining_videos)} 个视频待下载")

            # 等待页面加载完成
            await page.wait_for_timeout(5000)  # 增加等待时间确保页面完全加载

            # 保存当前页面的视频列表，因为在循环中会修改remaining_videos
            current_page_videos = remaining_videos.copy()

            # 创建设置当前下载视频信息的函数
            def set_current_video(video_info):
                nonlocal current_downloading_video_info, download_detected, video_id_map
                current_downloading_video_info = video_info
                download_detected = False  # 重置下载检测标志

                # 将视频信息添加到字典中，以便于后续匹配
                id_match = re.search(r'\d{6}', video_info['name'])
                if id_match:
                    video_id = id_match.group(0)
                    video_id_map[video_id] = video_info
                    print(f"浏览器 #{browser_id}: 将视频ID {video_id} 添加到字典")

                print(f"浏览器 #{browser_id}: 设置当前下载视频: {video_info['name']}, 模式: {video_info['mode']}")

            for video_info in current_page_videos[:]:  # 使用切片创建副本
                # 如果视频已被其他浏览器处理，则跳过
                if video_info not in remaining_videos:
                    print(f"浏览器 #{browser_id}: 视频 {video_info['name']} 已被其他浏览器处理，跳过")
                    continue
                    
                # 传递设置当前下载视频信息的函数
                success = await find_and_download_video(page, video_info, set_current_video)
                # 处理不同的返回值
                if success == 'GENERATING':
                    # 暗黑模式下生成中的视频
                    print(f"浏览器 #{browser_id}: ✗ 暗黑模式视频生成中，跳过: {video_info['name']}")
                    # 更新文件中的下载状态为"下载失败"
                    update_download_status(df, file_path, video_info, '下载失败')
                    download_results["失败"].append(video_info)
                    if video_info in remaining_videos:
                        remaining_videos.remove(video_info)  # 从待下载列表中移除
                elif success:
                    # 点击成功，增加等待时间检查是否真正检测到了下载
                    print(f"浏览器 #{browser_id}: 点击成功，等待30秒检查下载状态...")
                    await page.wait_for_timeout(10000)  # 增加到30秒，给下载事件更多时间触发

                    # 检查是否真正检测到了下载
                    if download_detected:
                        print(f"浏览器 #{browser_id}: ✓ 成功下载视频: {video_info['name']}")
                        # 注意: 完成状态已在handle_download函数中更新
                        if video_info not in download_results["成功"]:
                            download_results["成功"].append(video_info)
                        if video_info in remaining_videos:
                            remaining_videos.remove(video_info)
                    else:
                        print(f"浏览器 #{browser_id}: ✗ 点击了下载按钮但未检测到实际下载: {video_info['name']}")
                        # 更新文件中的下载状态为"下载失败"
                        update_download_status(df, file_path, video_info, '下载失败')
                        download_results["失败"].append(video_info)
                else:
                    print(f"浏览器 #{browser_id}: ✗ 未能下载视频: {video_info['name']}")
                    # 更新文件中的下载状态为"下载失败"
                    update_download_status(df, file_path, video_info, '下载失败')
                    download_results["失败"].append(video_info)

            # 处理未能下载的视频
            for video_info in remaining_videos[:]:  # 使用切片创建副本
                # 确保视频仍在待下载列表中（可能已被其他浏览器处理）
                if video_info in videos_to_download:
                    # 更新文件中的下载状态为"下载失败"
                    print(f"浏览器 #{browser_id}: 视频未找到，标记为下载失败: {video_info['name']}")
                    update_download_status(df, file_path, video_info, '下载失败')
                    download_results["失败"].append(video_info)
                    # 从全局待下载列表中移除
                    videos_to_download.remove(video_info)

        except Exception as e:
            print(f"浏览器 #{browser_id}: 程序执行出错: {e}")

        finally:
            # 在关闭浏览器之前等待一段时间，确保所有下载完成
            print(f"浏览器 #{browser_id}: 等待所有下载完成(5秒)...")
            await page.wait_for_timeout(5000)
            
            # 关闭浏览器
            await browser.close()
            print(f"浏览器 #{browser_id}: 浏览器已关闭")
            
    return download_results

async def main():
    # 检查依赖库
    check_dependencies()
    
    # 解析命令行参数
    args = parse_args()
    headless_mode = args.headless
    concurrent_browsers = args.concurrent if args.concurrent is not None else CONCURRENT_BROWSERS
    
    if headless_mode:
        print("使用无头模式运行浏览器（无界面）")
    else:
        print("使用有界面模式运行浏览器")
    
    print(f"设置并发浏览器数量为: {concurrent_browsers}")
    
    # 查找最新的创作任务文件夹
    latest_folder = find_latest_task_folder()
    if not latest_folder:
        print("未找到创作任务文件夹，程序终止")
        return

    # 查找Excel或CSV文件
    file_path = find_excel_file(latest_folder)
    if not file_path:
        print("未找到Excel或CSV文件，程序终止")
        return

    # 设置下载目录为文件所在的目录
    global DOWNLOAD_DIR
    DOWNLOAD_DIR = os.path.dirname(file_path)
    print(f"下载目录设置为: {DOWNLOAD_DIR}")

    # 创建积分和暗黑子文件夹
    points_dir = os.path.join(DOWNLOAD_DIR, "积分")
    dark_dir = os.path.join(DOWNLOAD_DIR, "暗黑")
    os.makedirs(points_dir, exist_ok=True)
    os.makedirs(dark_dir, exist_ok=True)
    print(f"创建积分目录: {points_dir}")
    print(f"创建暗黑目录: {dark_dir}")

    # 读取视频信息和数据帧
    video_info_list, df = read_video_names(file_path)
    if df is None:
        print("读取CSV文件失败，程序终止")
        return

    if not video_info_list:
        print("没有需要下载的视频，程序终止")
        return
    
    # 全局共享的待下载视频列表
    all_videos_to_download = video_info_list.copy()
    
    # 打印总共需要下载的视频数量
    print(f"总共有 {len(all_videos_to_download)} 个视频需要下载")
    print("注意: 程序将只处理第一页的视频，每页最多100条")
    
    # 如果视频数量超过100，提示用户
    if len(all_videos_to_download) > 100:
        print(f"警告: 视频数量({len(all_videos_to_download)})超过100，只会处理前100个视频")
        # 只保留前100个视频
        all_videos_to_download = all_videos_to_download[:100]
        print(f"已截取前100个视频进行处理")
    
    # 把视频列表分成多份，每个浏览器处理一部分
    videos_per_browser = max(1, len(all_videos_to_download) // concurrent_browsers)
    video_chunks = []
    
    # 修改：确保每个浏览器获得独立的视频任务，避免重复分配
    videos_assigned = set()  # 用于跟踪已分配的视频索引
    
    # 按顺序分配视频，确保积分模式视频优先处理
    for i in range(concurrent_browsers):
        chunk = []
        # 计算此浏览器应分配的视频数量
        chunk_size = videos_per_browser if i < concurrent_browsers - 1 else (len(all_videos_to_download) - len(videos_assigned))
        
        # 从未分配的视频中选择视频
        videos_count = 0
        for j, video in enumerate(all_videos_to_download):
            if j not in videos_assigned and videos_count < chunk_size:
                chunk.append(video)
                videos_assigned.add(j)
                videos_count += 1
        
        if chunk:  # 确保只添加非空的chunk
            video_chunks.append(chunk)
    
    # 打印分配情况，用于调试
    for i, chunk in enumerate(video_chunks):
        print(f"浏览器 #{i+1} 将处理 {len(chunk)} 个视频:")
        for video in chunk[:3]:  # 只显示前3个
            print(f"  - {video['name']}")
        if len(chunk) > 3:
            print(f"  ... 及其他 {len(chunk)-3} 个视频")
    
    # 调整为实际的浏览器数量（可能少于设定值）
    actual_browser_count = len(video_chunks)
    print(f"实际启动浏览器数量: {actual_browser_count} (每个浏览器约处理 {videos_per_browser} 个视频)")
    
    # 创建并发任务
    browser_tasks = []
    for i, videos_chunk in enumerate(video_chunks):
        task = process_videos_with_browser(
            videos_to_download=videos_chunk,  # 只传递该浏览器需要处理的视频子集
            file_path=file_path,
            df=df,
            headless_mode=headless_mode,
            browser_id=i+1
        )
        browser_tasks.append(task)
    
    # 并发执行所有浏览器任务
    print(f"开始并发下载，启动 {actual_browser_count} 个浏览器实例...")
    all_results = await asyncio.gather(*browser_tasks)
    
    # 合并所有浏览器的下载结果
    merged_results = {
        "成功": [],
        "失败": []
    }
    
    for result in all_results:
        for success_video in result["成功"]:
            if success_video not in merged_results["成功"]:
                merged_results["成功"].append(success_video)
        for failed_video in result["失败"]:
            if failed_video not in merged_results["失败"] and failed_video not in merged_results["成功"]:
                merged_results["失败"].append(failed_video)
    
    # 打印下载结果
    print("\n===== 下载结果 =====")

    # 从数据帧中获取最新的完成状态统计
    total_videos = len(df)
    success_count = sum(1 for status in df['完成状态'] if status == '已交付')
    fail_count = sum(1 for status in df['完成状态'] if status == '下载失败')
    pending_count = total_videos - success_count - fail_count

    print(f"总视频数: {total_videos} 个")
    print(f"成功下载: {success_count} 个 ({success_count/total_videos*100:.1f}%)")
    print(f"下载失败: {fail_count} 个 ({fail_count/total_videos*100:.1f}%)")
    print(f"未处理: {pending_count} 个 ({pending_count/total_videos*100:.1f}%)")

    # 按模式统计成功下载的视频
    if success_count > 0:
        print("\n成功下载的视频按模式统计:")
        # 创建一个新的数据帧，只包含成功下载的视频
        success_df = df[df['完成状态'] == '已交付']
        # 判断模式
        if '生成模式' in df.columns:
            dark_success = sum(1 for _, row in success_df.iterrows() if '暗黑' in str(row.get('生成模式', '')))
            points_success = success_count - dark_success
            print(f"  积分模式: {points_success} 个")
            print(f"  暗黑模式: {dark_success} 个")

    # 按模式统计失败的视频
    if fail_count > 0:
        print("\n下载失败的视频按模式统计:")
        # 创建一个新的数据帧，只包含失败的视频
        fail_df = df[df['完成状态'] == '下载失败']
        # 判断模式
        if '生成模式' in df.columns:
            dark_fail = sum(1 for _, row in fail_df.iterrows() if '暗黑' in str(row.get('生成模式', '')))
            points_fail = fail_count - dark_fail
            print(f"  积分模式: {points_fail} 个")
            print(f"  暗黑模式: {dark_fail} 个")

        # 显示失败的视频名称
        print("\n下载失败的视频:")
        for _, row in fail_df.iterrows():
            mode = '暗黑' if '生成模式' in df.columns and '暗黑' in str(row.get('生成模式', '')) else '积分'
            print(f"  - {row['名称']} (模式: {mode})")

if __name__ == "__main__":
    asyncio.run(main())
