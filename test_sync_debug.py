#!/usr/bin/env python3
"""
调试百度表格同步功能
"""

import sys
import os
import asyncio

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

class LogCapture:
    """捕获日志消息"""
    def __init__(self):
        self.messages = []
    
    def emit(self, message):
        print(f"[LOG] {message}")
        self.messages.append(message)

async def test_sync_debug():
    """调试同步功能"""
    try:
        from core.voice_manager import VoiceManager
        from core.config_manager import ConfigManager
        
        print("✓ 模块导入成功")
        
        # 创建实例
        vm = VoiceManager()
        config_manager = ConfigManager()
        
        # 设置日志捕获
        log_capture = LogCapture()
        vm.log_message = log_capture
        
        # 检查配置
        print("\n✓ 检查配置:")
        baidu_sheet_url = config_manager.get("baidu_sheet_url", "")
        baidu_sheets_token = config_manager.get("baidu_sheets_token", "")
        
        print(f"  - URL: {baidu_sheet_url}")
        print(f"  - Token: {baidu_sheets_token}")
        
        if not baidu_sheet_url or not baidu_sheets_token:
            print("❌ 配置不完整")
            return False
        
        # 创建测试数据
        test_models = [
            {
                'name': '调试测试声音',
                'modelId': 'debug_test_001',
                'url': 'https://fish.audio/model/debug_test_001',
                'extractTime': '2024-12-01 10:45:00'
            }
        ]
        
        print(f"\n✓ 测试数据: {test_models}")
        
        # 执行同步
        print("\n✓ 开始同步调试...")
        
        try:
            await vm._sync_to_baidu_table_async(test_models)
        except Exception as sync_error:
            print(f"❌ 同步异常: {sync_error}")
            import traceback
            traceback.print_exc()
        
        # 显示所有日志
        print("\n📋 完整日志:")
        for i, msg in enumerate(log_capture.messages, 1):
            print(f"  {i}. {msg}")
        
        print("\n🎉 调试测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 调试测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚀 开始百度表格同步调试...\n")
    
    success = asyncio.run(test_sync_debug())
    
    print("\n" + "="*50 + "\n")
    
    if success:
        print("🎉 调试测试完成！")
        print("\n💡 请查看上面的详细日志来诊断问题")
    else:
        print("❌ 调试测试失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
