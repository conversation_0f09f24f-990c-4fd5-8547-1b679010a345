#!/usr/bin/env python3
"""
声音管理功能测试脚本
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_voice_manager():
    """测试声音管理器"""
    try:
        from core.voice_manager import VoiceManager
        
        print("✓ 声音管理器导入成功")
        
        # 创建声音管理器实例
        vm = VoiceManager()
        print("✓ 声音管理器实例创建成功")
        
        # 测试目录初始化
        print(f"✓ 声音数据目录: {vm.VOICE_DATA_DIR}")
        print(f"✓ 音频上传目录: {vm.AUDIO_UPLOAD_DIR}")
        print(f"✓ 已上传文件目录: {vm.AUDIO_UPLOADED_DIR}")
        
        # 测试数据加载
        models = vm.get_voice_models()
        print(f"✓ 当前声音模型数量: {len(models)}")
        
        # 测试打开音频文件夹
        print("✓ 测试打开音频文件夹...")
        vm.open_audio_folder()
        
        print("\n🎉 声音管理器测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 声音管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window_import():
    """测试主窗口导入"""
    try:
        from ui.main_window import MainWindow
        print("✓ 主窗口导入成功")
        return True
    except Exception as e:
        print(f"❌ 主窗口导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始测试声音管理功能...\n")
    
    # 测试声音管理器
    print("1. 测试声音管理器...")
    vm_success = test_voice_manager()
    
    print("\n" + "="*50 + "\n")
    
    # 测试主窗口导入
    print("2. 测试主窗口导入...")
    mw_success = test_main_window_import()
    
    print("\n" + "="*50 + "\n")
    
    # 总结
    if vm_success and mw_success:
        print("🎉 所有测试通过！声音管理功能已成功集成。")
        print("\n📋 使用说明:")
        print("1. 运行主程序: python src/main.py")
        print("2. 在左侧侧边栏找到'声音管理'按钮")
        print("3. 点击进入声音管理页面")
        print("4. 使用'音频位置'按钮打开音频文件夹")
        print("5. 使用'获取ID'按钮从Fish Audio获取声音模型")
        print("6. 使用'ID同步'按钮同步到百度表格（待实现）")
    else:
        print("❌ 部分测试失败，请检查错误信息。")
        sys.exit(1)
