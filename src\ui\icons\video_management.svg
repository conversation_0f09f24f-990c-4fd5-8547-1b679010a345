<?xml version="1.0" encoding="UTF-8"?>
<svg id="_图层_1" data-name="图层 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
  <defs>
    <style>
      .cls-1 {
        stroke: #000;
        stroke-miterlimit: 10;
        stroke-width: 2px;
      }
    </style>
  </defs>
  <!-- 视频播放器外框 -->
  <rect class="cls-1" x="30" y="60" width="140" height="80" rx="8" ry="8" fill="none"/>
  
  <!-- 播放按钮 -->
  <polygon points="85,85 85,115 115,100" fill="#000"/>
  
  <!-- 管理文件夹图标 -->
  <rect class="cls-1" x="50" y="30" width="100" height="15" rx="3" ry="3" fill="none"/>
  <rect class="cls-1" x="40" y="40" width="120" height="20" rx="3" ry="3" fill="none"/>
  
  <!-- 底部管理条 -->
  <rect class="cls-1" x="30" y="150" width="140" height="20" rx="3" ry="3" fill="none"/>
  
  <!-- 管理点 -->
  <circle cx="50" cy="160" r="3" fill="#000"/>
  <circle cx="70" cy="160" r="3" fill="#000"/>
  <circle cx="90" cy="160" r="3" fill="#000"/>
</svg>
