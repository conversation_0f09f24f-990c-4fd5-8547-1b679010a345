#!/usr/bin/env python3
"""
测试Fish Audio上传格式
"""

import sys
import os
import asyncio
import httpx

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_upload_format():
    """测试上传格式"""
    try:
        from core.config_manager import ConfigManager
        
        print("✓ 配置管理器导入成功")
        
        # 创建配置管理器实例
        config_manager = ConfigManager()
        api_key = config_manager.get("api_key", "")
        
        if not api_key:
            print("❌ 未配置API密钥")
            return False
        
        print(f"✓ API密钥: {api_key[:8]}...")
        
        # 创建测试音频文件（如果不存在）
        test_audio_dir = "voice_management/audio_files"
        os.makedirs(test_audio_dir, exist_ok=True)
        
        test_file = os.path.join(test_audio_dir, "test.mp3")
        if not os.path.exists(test_file):
            # 创建一个小的测试文件
            with open(test_file, 'wb') as f:
                f.write(b'fake mp3 content for testing')
            print(f"✓ 创建测试文件: {test_file}")
        
        # 测试API调用格式
        api_endpoint = "https://api.fish.audio/model"
        
        print("✓ 测试API调用格式...")
        
        # 准备数据（不实际发送）
        files = [
            ('voices', open(test_file, 'rb'))
        ]
        
        data = [
            ('title', 'test'),
            ('type', 'tts'),
            ('visibility', 'private'),
            ('train_mode', 'fast'),
            ('enhance_audio_quality', 'true')
        ]
        
        print("✓ 文件参数:", [name for name, _ in files])
        print("✓ 数据参数:", dict(data))
        
        # 关闭文件
        for _, file_obj in files:
            if hasattr(file_obj, 'close'):
                file_obj.close()
        
        print("\n🎉 上传格式测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 上传格式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚀 开始测试Fish Audio上传格式...\n")
    
    success = asyncio.run(test_upload_format())
    
    print("\n" + "="*50 + "\n")
    
    if success:
        print("🎉 测试通过！上传格式正确。")
        print("\n📋 修复内容:")
        print("1. type: 'svc' → 'tts'")
        print("2. file → voices")
        print("3. 添加 enhance_audio_quality")
        print("4. 使用正确的表单数据格式")
    else:
        print("❌ 测试失败，请检查错误信息。")
        sys.exit(1)

if __name__ == "__main__":
    main()
