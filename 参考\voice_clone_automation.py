import pandas as pd
import time
import asyncio
from playwright.async_api import async_playwright
import os
import shutil
from datetime import datetime
import glob

async def main():
    # 创建以当天日期命名的日志文件夹
    date_today = datetime.now().strftime("%Y%m%d")
    log_folder = f"克隆声音记录_{date_today}"
    os.makedirs(log_folder, exist_ok=True)
    print(f"创建日志文件夹: {log_folder}")
    
    # 创建已完成文件夹
    completed_folder = os.path.join(log_folder, "已完成")
    os.makedirs(completed_folder, exist_ok=True)
    print(f"创建已完成文件夹: {completed_folder}")
    
    # 读取需要克隆的音频文件
    audio_folder = "克隆音频文件"  # 存放音频文件的文件夹
    if not os.path.exists(audio_folder):
        os.makedirs(audio_folder, exist_ok=True)
        print(f"创建音频文件夹: {audio_folder}")
        print(f"请将需要克隆的音频文件放到 '{audio_folder}' 文件夹中，然后重新运行脚本")
        return
    
    # 获取所有音频文件
    audio_files = []
    for ext in ['*.mp3', '*.wav', '*.flac', '*.m4a']:
        audio_files.extend(glob.glob(os.path.join(audio_folder, ext)))
    
    if not audio_files:
        print(f"未在 '{audio_folder}' 文件夹中找到音频文件")
        print("支持的格式: mp3, wav, flac, m4a")
        return
    
    print(f"找到 {len(audio_files)} 个音频文件待克隆")
    
    # 创建或加载状态记录文件
    status_file = os.path.join(log_folder, "克隆状态记录.xlsx")
    if os.path.exists(status_file):
        try:
            df = pd.read_excel(status_file)
            print(f"加载现有状态记录，共 {len(df)} 条记录")
        except Exception as e:
            print(f"加载状态记录失败: {e}")
            df = pd.DataFrame({"音频文件": [], "状态": [], "创建时间": [], "备注": []})
    else:
        df = pd.DataFrame({"音频文件": [], "状态": [], "创建时间": [], "备注": []})
    
    # 确保必要的列存在
    required_columns = ["音频文件", "状态", "创建时间", "备注"]
    for col in required_columns:
        if col not in df.columns:
            df[col] = ""
    
    # 使用 Playwright 连接到已有的浏览器实例
    async with async_playwright() as p:
        print("尝试连接到调试端口9222的浏览器...")
        
        # 尝试不同的方式连接浏览器
        browser = None
        try:
            # 先尝试使用localhost连接
            browser = await p.chromium.connect_over_cdp("http://localhost:9222")
            print("成功通过localhost连接到浏览器")
        except Exception as e:
            print(f"通过localhost连接失败: {e}")
            try:
                # 如果localhost连接失败，尝试使用IP地址连接
                print("尝试通过IP地址连接...")
                browser = await p.chromium.connect_over_cdp("http://127.0.0.1:9222")
                print("成功通过IP地址连接到浏览器")
            except Exception as e2:
                print(f"通过IP地址连接也失败: {e2}")
                print("无法连接到浏览器。请确保Chrome已以调试模式启动：")
                print("chrome.exe --remote-debugging-port=9222")
                return
        
        # 如果浏览器连接成功，继续执行自动化流程
        if browser:
            try:
                # 获取浏览器上下文和页面
                contexts = browser.contexts
                if not contexts or len(contexts) == 0:
                    print("未找到浏览器上下文，无法继续")
                    return
                
                # 使用第一个上下文
                context = contexts[0]
                print(f"找到浏览器上下文")
                
                # 从上下文中获取已打开的页面
                pages = context.pages
                if pages and len(pages) > 0:
                    # 使用第一个已打开的页面
                    page = pages[0]
                    print(f"使用已打开的页面: {page.url}")
                else:
                    # 如果没有页面，才创建新页面
                    print("没有找到已打开的页面，创建新页面")
                    page = await context.new_page()
                
                # 设置更长的超时时间
                page.set_default_timeout(60000)  # 将默认超时时间设置为60秒
                
                # 检查是否处于登录状态，如果需要登录则等待手动登录
                if page.url.startswith("https://fish.audio/zh-CN/auth/"):
                    print("检测到登录页面，请在浏览器中手动登录...")
                    
                    # 等待用户登录
                    login_timeout = 120  # 等待2分钟
                    login_successful = False
                    
                    for _ in range(login_timeout):
                        if not page.url.startswith("https://fish.audio/zh-CN/auth/"):
                            login_successful = True
                            print("✅ 检测到登录完成")
                            break
                        await page.wait_for_timeout(1000)
                    
                    if not login_successful:
                        print("❌ 登录等待超时，终止执行")
                        return
                
                # 针对每个音频文件进行处理
                for audio_file in audio_files:
                    # 获取文件基本信息
                    file_basename = os.path.basename(audio_file)
                    file_name_without_ext = os.path.splitext(file_basename)[0]
                    
                    # 检查此文件是否已经处理过
                    file_records = df[df["音频文件"] == file_basename]
                    if not file_records.empty and file_records["状态"].iloc[0] == "克隆完成":
                        print(f"跳过已处理的文件: {file_basename}")
                        continue
                    
                    print(f"\n==== 处理音频文件: {file_basename} ====")
                    
                    try:
                        # 导航到创建声音模型页面
                        await page.goto("https://fish.audio/zh-CN/train/new-model/", timeout=30000)
                        await page.wait_for_selector("body", state="visible", timeout=10000)
                        
                        # 检查是否被重定向到登录页面
                        if page.url.startswith("https://fish.audio/zh-CN/auth/"):
                            print("被重定向到登录页面，请在浏览器中手动登录")
                            await page.wait_for_timeout(60000)  # 等待1分钟供用户登录
                            if page.url.startswith("https://fish.audio/zh-CN/auth/"):
                                print("登录超时，跳过当前文件")
                                continue
                            
                            # 登录后重新导航到创建声音模型页面
                            await page.goto("https://fish.audio/zh-CN/train/new-model/", timeout=30000)
                            await page.wait_for_selector("body", state="visible", timeout=10000)
                        
                        # 等待页面加载完成
                        await page.wait_for_timeout(3000)
                        
                        # 1. 选择"不公开展示"选项卡
                        print("选择'不公开展示'选项卡...")
                        try:
                            # 寻找"不公开展示"选项卡
                            unlist_tab = await page.query_selector('button[role="tab"]:has-text("不公开展示")')
                            if unlist_tab:
                                await unlist_tab.click()
                                print("✅ 已选择'不公开展示'选项卡")
                                await page.wait_for_timeout(1000)  # 等待选项卡切换
                            else:
                                print("⚠️ 未找到'不公开展示'选项卡，尝试通过ID选择...")
                                # 尝试通过ID选择
                                await page.click('#radix-\\:ra1\\:-trigger-unlist')
                                print("通过ID选择'不公开展示'选项卡")
                        except Exception as e:
                            print(f"选择'不公开展示'选项卡时出错: {e}")
                            # 尝试使用JavaScript点击
                            try:
                                await page.evaluate('''
                                    const tabs = Array.from(document.querySelectorAll('button[role="tab"]'));
                                    const unlistTab = tabs.find(tab => tab.textContent.includes('不公开展示'));
                                    if (unlistTab) unlistTab.click();
                                ''')
                                print("通过JavaScript选择'不公开展示'选项卡")
                            except Exception as js_error:
                                print(f"通过JavaScript选择选项卡也失败: {js_error}")
                        
                        # 2. 填写名称输入框
                        print(f"填写名称: {file_name_without_ext}...")
                        name_input = await page.query_selector('input[name="title"]')
                        if not name_input:
                            # 尝试其他选择器
                            name_input = await page.query_selector('input[placeholder="填写声音名称"]')
                            if not name_input:
                                # 尝试更通用的选择器
                                name_input = await page.query_selector('.mt-\\[0\\.375rem\\]')
                        
                        if name_input:
                            await name_input.fill(file_name_without_ext)
                            print("✅ 名称已填写")
                        else:
                            print("❌ 未找到名称输入框")
                            await page.screenshot(path=os.path.join(log_folder, f"{file_basename}_name_input_missing.png"))
                            new_row = pd.DataFrame({
                                "音频文件": [file_basename],
                                "状态": ["失败"],
                                "创建时间": [datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
                                "备注": ["未找到名称输入框"]
                            })
                            df = pd.concat([df, new_row], ignore_index=True)
                            continue
                        
                        # 3. 上传音频文件
                        print(f"准备上传音频文件...")
                        
                        # 找到文件上传输入元素
                        upload_input = await page.query_selector('input[type="file"][accept="audio/*,.wav,.mp3,.flac,.m4a"]')
                        if not upload_input:
                            # 尝试查找隐藏的文件输入
                            upload_input = await page.query_selector('input[type="file"].hidden')
                        
                        if upload_input:
                            # 上传文件
                            await upload_input.set_input_files(audio_file)
                            print(f"✅ 已上传文件: {file_basename}")
                            
                            # 等待文件上传完成 - 寻找上传进度指示器或成功指示
                            print("等待文件上传处理...")
                            await page.wait_for_timeout(5000)  # 基础等待时间
                            
                            # 检查是否有上传成功指示
                            success_indicator = await page.query_selector('div:has-text("已上传")')
                            if success_indicator:
                                print("✅ 文件已成功上传并处理")
                            else:
                                # 等待更长时间，对于较大的文件
                                file_size_mb = os.path.getsize(audio_file) / (1024 * 1024)
                                additional_wait = min(int(file_size_mb * 1000), 30000)  # 每MB额外等待1秒，最多30秒
                                print(f"文件大小: {file_size_mb:.2f}MB，额外等待 {additional_wait/1000:.1f} 秒")
                                await page.wait_for_timeout(additional_wait)
                            
                            # 取消勾选"去除伴奏"复选框
                            try:
                                print("检查并取消'去除伴奏'复选框...")
                                # 查找去除伴奏复选框
                                enhance_checkbox = await page.query_selector('#enhance_audio_quality_checkbox')
                                
                                # 如果没找到通过ID，尝试通过标签文本查找
                                if not enhance_checkbox:
                                    enhance_checkbox = await page.query_selector('button[role="checkbox"]:near(:text("去除伴奏"))')
                                
                                if enhance_checkbox:
                                    # 检查复选框当前状态
                                    is_checked = await enhance_checkbox.get_attribute('aria-checked')
                                    
                                    if is_checked == "true":
                                        # 如果是选中状态，则点击取消选中
                                        await enhance_checkbox.click()
                                        print("✅ 已取消勾选'去除伴奏'复选框")
                                        # 等待状态更新
                                        await page.wait_for_timeout(1000)
                                    else:
                                        print("'去除伴奏'复选框已经是未选中状态")
                                else:
                                    print("⚠️ 未找到'去除伴奏'复选框，继续执行")
                            except Exception as checkbox_error:
                                print(f"处理'去除伴奏'复选框时出错: {checkbox_error}")
                                print("继续执行其他步骤...")
                        else:
                            print("❌ 未找到文件上传输入元素")
                            await page.screenshot(path=os.path.join(log_folder, f"{file_basename}_upload_input_missing.png"))
                            new_row = pd.DataFrame({
                                "音频文件": [file_basename],
                                "状态": ["失败"],
                                "创建时间": [datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
                                "备注": ["未找到文件上传输入元素"]
                            })
                            df = pd.concat([df, new_row], ignore_index=True)
                            continue
                        
                        # 4. 点击"创建"按钮
                        print("查找并点击'创建'按钮...")
                        create_button = await page.query_selector('button:has-text("创建")')
                        if create_button:
                            # 检查按钮是否可点击
                            is_disabled = await create_button.get_attribute('disabled')
                            if is_disabled:
                                print("⚠️ '创建'按钮当前不可点击，可能文件仍在处理中")
                                # 额外等待让文件完成处理
                                await page.wait_for_timeout(10000)
                                # 重新检查按钮状态
                                is_disabled = await create_button.get_attribute('disabled')
                                if is_disabled:
                                    print("❌ '创建'按钮仍不可点击，跳过此文件")
                                    new_row = pd.DataFrame({
                                        "音频文件": [file_basename],
                                        "状态": ["失败"],
                                        "创建时间": [datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
                                        "备注": ["创建按钮不可点击"]
                                    })
                                    df = pd.concat([df, new_row], ignore_index=True)
                                    continue
                            
                            # 点击创建按钮
                            await create_button.click()
                            print("✅ 已点击'创建'按钮")
                            
                            # 等待创建完成 - 通常会跳转到另一个页面或显示成功消息
                            print("等待声音克隆完成...")
                            
                            # 等待页面变化或成功消息
                            success = False
                            for _ in range(60):  # 最多等待60秒
                                # 检查URL变化 - 如果跳转到我的声音库，说明成功了
                                if "my-library" in page.url:
                                    print("✅ 检测到页面跳转到声音库，克隆已提交")
                                    success = True
                                    break
                                
                                # 或者检查成功消息
                                success_msg = await page.query_selector('div:has-text("声音克隆成功")')
                                if success_msg:
                                    print("✅ 检测到'声音克隆成功'消息")
                                    success = True
                                    break
                                
                                # 检查是否出现保存/跳过按钮页面
                                save_button = await page.query_selector('button:has-text("保存")')
                                skip_button = await page.query_selector('button:has-text("跳过")')
                                if save_button and skip_button:
                                    print("✅ 检测到保存/跳过选项页面")
                                    success = True
                                    break
                                
                                await page.wait_for_timeout(1000)
                            
                            if success:
                                # 尝试处理保存/跳过按钮
                                try:
                                    # 先检查是否有保存/跳过按钮
                                    save_button = await page.query_selector('button:has-text("保存")')
                                    skip_button = await page.query_selector('button:has-text("跳过")')
                                    
                                    if save_button and skip_button:
                                        print("检测到保存/跳过选项，尝试点击保存...")
                                        
                                        # 尝试点击保存按钮
                                        await save_button.click()
                                        print("已点击保存按钮")
                                        
                                        # 等待看是否有错误提示
                                        await page.wait_for_timeout(3000)
                                        
                                        # 检查是否有错误提示
                                        error_msg = await page.query_selector('div.text-destructive, div:has-text("请填写所有必填项")')
                                        
                                        # 如果有错误提示，则点击跳过按钮
                                        if error_msg:
                                            print("检测到保存错误，尝试点击跳过...")
                                            skip_button = await page.query_selector('button:has-text("跳过")')
                                            if skip_button:
                                                await skip_button.click()
                                                print("已点击跳过按钮")
                                            else:
                                                print("⚠️ 未找到跳过按钮")
                                    
                                    # 等待一段时间让页面跳转完成
                                    await page.wait_for_timeout(5000)
                                except Exception as e:
                                    print(f"处理保存/跳过按钮时出错: {e}")
                                
                                new_row = pd.DataFrame({
                                    "音频文件": [file_basename],
                                    "状态": ["克隆完成"],
                                    "创建时间": [datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
                                    "备注": ["成功"]
                                })
                                df = pd.concat([df, new_row], ignore_index=True)
                                
                                # 将成功处理的音频文件移动到已完成文件夹
                                try:
                                    dest_path = os.path.join(completed_folder, file_basename)
                                    shutil.move(audio_file, dest_path)
                                    print(f"✅ 已将音频文件 {file_basename} 移动到已完成文件夹")
                                except Exception as move_error:
                                    print(f"移动音频文件时出错: {move_error}")
                            else:
                                print("⚠️ 未明确检测到克隆成功，但流程已执行")
                                # 保存当前页面截图以供检查
                                await page.screenshot(path=os.path.join(log_folder, f"{file_basename}_completion_uncertain.png"))
                                new_row = pd.DataFrame({
                                    "音频文件": [file_basename],
                                    "状态": ["不确定"],
                                    "创建时间": [datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
                                    "备注": ["未明确检测到成功状态"]
                                })
                                df = pd.concat([df, new_row], ignore_index=True)
                        else:
                            print("❌ 未找到'创建'按钮")
                            await page.screenshot(path=os.path.join(log_folder, f"{file_basename}_create_button_missing.png"))
                            new_row = pd.DataFrame({
                                "音频文件": [file_basename],
                                "状态": ["失败"],
                                "创建时间": [datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
                                "备注": ["未找到创建按钮"]
                            })
                            df = pd.concat([df, new_row], ignore_index=True)
                    except Exception as e:
                        print(f"处理文件 {file_basename} 时出错: {e}")
                        await page.screenshot(path=os.path.join(log_folder, f"{file_basename}_error.png"))
                        new_row = pd.DataFrame({
                            "音频文件": [file_basename],
                            "状态": ["错误"],
                            "创建时间": [datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
                            "备注": [str(e)[:200]]
                        })
                        df = pd.concat([df, new_row], ignore_index=True)
                    
                    # 保存每个文件处理后的状态
                    df.to_excel(status_file, index=False)
                    print(f"已保存处理状态到: {status_file}")
                    
                    # 等待一段时间再处理下一个文件
                    await page.wait_for_timeout(5000)
                
                print("\n===== 全部文件处理完成 =====")
                print(f"处理记录已保存到: {status_file}")
                
                # 生成完成状态表格
                try:
                    # 筛选出已完成的记录
                    completed_df = df[df["状态"] == "克隆完成"].copy()
                    
                    if not completed_df.empty:
                        # 提取演员名称 - 从文件名中提取
                        completed_df["演员名称"] = completed_df["音频文件"].apply(
                            lambda x: os.path.splitext(x)[0]  # 去掉扩展名
                        )
                        
                        # 创建简化的完成状态表
                        completion_report = pd.DataFrame({
                            "演员名称": completed_df["演员名称"],
                            "完成状态": "已完成"
                        })
                        
                        # 保存到Excel
                        completion_report_path = os.path.join(log_folder, f"克隆完成状态_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
                        completion_report.to_excel(completion_report_path, index=False)
                        print(f"✅ 已生成完成状态表格: {completion_report_path}")
                    else:
                        print("⚠️ 没有已完成的克隆记录，未生成状态表格")
                except Exception as e:
                    print(f"生成完成状态表格时出错: {e}")
                
            except Exception as e:
                print(f"自动化过程出现严重错误: {e}")
                # 捕获错误截图
                if 'page' in locals() and page:
                    await page.screenshot(path=os.path.join(log_folder, "critical_error.png"))

if __name__ == "__main__":
    asyncio.run(main()) 