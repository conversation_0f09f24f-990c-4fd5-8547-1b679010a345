#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
配置管理器，用于管理应用程序配置
"""

import os
import json
from datetime import datetime
import config
from PySide6.QtCore import QObject, Signal

class ConfigManager(QObject):
    """配置管理器类，用于管理应用程序配置"""
    
    # 定义信号
    config_changed = Signal()  # 配置变更信号
    theme_changed = Signal(bool)  # 主题变更信号，传递是否为暗色主题
    
    def __init__(self):
        """初始化配置管理器"""
        super().__init__()
        
        # 配置文件路径
        self.config_file = os.path.join("config", "settings.json")
        
        # 确保配置目录存在
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        
        # 默认配置
        self.default_config = {
            "api_key": config.API_KEY,
            "api_endpoint": "https://api.fish.audio/v1/tts",
            "model": "speech-1.6",
            "max_concurrent_tasks": 3,
            "audio_format": config.AUDIO_FORMAT,
            "mp3_bitrate": config.MP3_BITRATE,
            "chunk_length": 200,
            "normalize_audio": True,
            "latency": "normal",
            "voice_id_excel": config.VOICE_ID_EXCEL,
            "script_excel": config.SCRIPT_EXCEL,
            "output_dir": "output",
            "enable_clipboard_monitoring": True,
            "enable_fuzzy_actor_matching": True,
            "dark_mode": False,
            "sidebar_collapsed": False,
            "use_online_sheet": False,
            "baidu_sheet_url": "",
            "use_baidu_api": True,
            "baidu_sheets_token": "",
            "baidu_page_size": 1000,
            "use_baidu_cache": True,
            "cache_ttl_seconds": 3600,  # 缓存有效期1小时
            "last_work_date": "",
            "last_work_folder": "",
            "enable_clipboard": True,
            "enable_text_conversion": True,  # 启用文案自动转换
            "convert_numbers": True,  # 启用数字转汉字
            "api_batch_size": 6,  # API批次大小（每批处理的API任务数量）
            "hifly_api_token": "",  # HiFly API令牌
            "video_processing_mode": "watermark_removal",  # 视频处理方案：watermark_removal(去水印) 或 cropping(裁剪)
            "watermark_regions": [  # 默认水印区域
                {"x": 30, "y": 27, "width": 310, "height": 60},    # 左上角AI文字
                {"x": 1574, "y": 961, "width": 308, "height": 97}  # 右下角飞影数字人
            ],
            "video_processing_concurrent": 3,  # 视频处理并发数量（同时处理的视频数量）
            # 水印去除羽化配置（优化后的精确参数）
            "feather_radius": 5,      # 羽化半径（像素）：控制边缘软化程度，5px提供良好的边缘融合
            "feather_sigma": 1.5,     # 高斯模糊强度：控制羽化的平滑度，1.5提供适中的平滑效果
            "edge_blur_radius": 8,    # 边缘模糊半径：控制边缘模糊范围，8px避免边缘泄露
            # 视频下载配置
            "video_download_concurrent_browsers": 3,  # 视频下载并发浏览器数量
            "video_download_search_days": 3,  # 查找最近几天的任务文件夹
            "video_download_enable_screenshots": False  # 视频下载时是否启用截图
        }
        
        # 加载配置
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, "r", encoding="utf-8") as f:
                    loaded_config = json.load(f)
                    
                    # 使用默认配置作为基础，然后更新已加载的配置
                    config_dict = self.default_config.copy()
                    config_dict.update(loaded_config)
                    self.current_config = config_dict
                    return self.current_config
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                self.current_config = self.default_config.copy()
                return self.current_config
        else:
            self.current_config = self.default_config.copy()
            return self.current_config
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(self.current_config, f, ensure_ascii=False, indent=4)
            self.config_changed.emit()
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get(self, key, default=None):
        """获取配置项"""
        value = self.current_config.get(key, default)

        # 对声音ID文件路径进行特殊处理，统一使用data目录下的文件
        if key == "voice_id_excel":
            # 始终使用默认的data目录下的文件
            voice_id_path = config.VOICE_ID_EXCEL

            # 如果文件不存在，尝试使用上级目录的路径（开发模式）
            if not os.path.exists(voice_id_path):
                parent_path = os.path.join("..", voice_id_path)
                if os.path.exists(parent_path):
                    return parent_path

            return voice_id_path

        return value
    
    def set(self, key, value):
        """设置配置项"""
        if key in self.current_config and self.current_config[key] == value:
            return False  # 值未变更
        
        old_value = self.current_config.get(key)
        self.current_config[key] = value
        changed = self.save_config()
        
        # 如果更改了主题模式，发出主题变更信号
        if key == "dark_mode" and old_value != value:
            self.theme_changed.emit(value)
            
        return changed
    
    def toggle_dark_mode(self):
        """切换暗色/亮色模式"""
        current_mode = self.get("dark_mode", False)
        return self.set("dark_mode", not current_mode)
    
    def save(self):
        """保存当前配置到文件"""
        return self.save_config()
        
    def reset_to_defaults(self):
        """重置为默认配置"""
        # 保存当前主题设置和侧边栏状态
        current_dark_mode = self.current_config.get("dark_mode", False)
        current_sidebar_collapsed = self.current_config.get("sidebar_collapsed", False)
        
        # 重置配置
        self.current_config = self.default_config.copy()
        
        # 恢复主题设置和侧边栏状态
        self.current_config["dark_mode"] = current_dark_mode
        self.current_config["sidebar_collapsed"] = current_sidebar_collapsed
        
        changed = self.save_config()
        return changed
    
    def create_daily_work_folder(self):
        """创建当天的工作文件夹（只创建主文件夹和Excel文件，不创建配音文件夹）"""
        # 获取当前日期
        date_today = datetime.now().strftime("%Y%m%d")

        # 获取输出目录（确保使用绝对路径）
        output_dir = self.get("output_dir", config.OUTPUT_DIR)
        if not os.path.isabs(output_dir):
            # 如果是相对路径，则相对于项目根目录
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            output_dir = os.path.join(project_root, output_dir)

        os.makedirs(output_dir, exist_ok=True)

        # 创建以当天日期命名的主文件夹（在输出目录下）
        main_folder = os.path.join(output_dir, f"生成结果_{date_today}")
        os.makedirs(main_folder, exist_ok=True)

        # 创建当天的文案Excel文件路径（不创建配音文件夹）
        daily_excel = os.path.join(main_folder, f"文案_{date_today}.xlsx")

        # 更新最后工作日期和文件夹
        self.set("last_work_date", date_today)
        self.set("last_work_folder", main_folder)

        return {
            "main_folder": main_folder,
            "daily_excel": daily_excel
        }

    def get_or_create_download_folder(self):
        """获取或创建当天的配音文件夹（用于开始处理时）"""
        # 获取当前日期
        date_today = datetime.now().strftime("%Y%m%d")

        # 获取输出目录
        output_dir = self.get("output_dir", config.OUTPUT_DIR)
        if not os.path.isabs(output_dir):
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            output_dir = os.path.join(project_root, output_dir)

        # 确保主文件夹存在
        main_folder = os.path.join(output_dir, f"生成结果_{date_today}")
        os.makedirs(main_folder, exist_ok=True)

        # 检查是否已经有当天的配音文件夹
        existing_folders = []
        if os.path.exists(main_folder):
            for item in os.listdir(main_folder):
                if item.startswith(f"配音文件_{date_today}_") and os.path.isdir(os.path.join(main_folder, item)):
                    existing_folders.append(item)

        if existing_folders:
            # 如果已经有配音文件夹，使用最新的一个
            existing_folders.sort()
            download_folder = os.path.join(main_folder, existing_folders[-1])
        else:
            # 如果没有，创建新的配音文件夹
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            download_folder = os.path.join(main_folder, f"配音文件_{timestamp}")
            os.makedirs(download_folder, exist_ok=True)

        return download_folder

    def get_output_folder_for_open(self):
        """获取要打开的输出文件夹（用于输出位置按钮）"""
        # 获取当前日期
        date_today = datetime.now().strftime("%Y%m%d")

        # 获取输出目录
        output_dir = self.get("output_dir", config.OUTPUT_DIR)
        if not os.path.isabs(output_dir):
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            output_dir = os.path.join(project_root, output_dir)

        # 主文件夹路径
        main_folder = os.path.join(output_dir, f"生成结果_{date_today}")

        # 优先级1：检查是否有配音文件夹
        if os.path.exists(main_folder):
            existing_folders = []
            for item in os.listdir(main_folder):
                if item.startswith(f"配音文件_{date_today}_") and os.path.isdir(os.path.join(main_folder, item)):
                    existing_folders.append(item)

            if existing_folders:
                # 如果有配音文件夹，打开最新的一个
                existing_folders.sort()
                return os.path.join(main_folder, existing_folders[-1])

        # 优先级2：检查是否有生成结果文件夹
        if os.path.exists(main_folder):
            return main_folder

        # 优先级3：创建生成结果文件夹
        os.makedirs(main_folder, exist_ok=True)
        return main_folder