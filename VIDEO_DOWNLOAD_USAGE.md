# 视频下载功能使用说明

## 功能概述

新增的视频下载功能位于数字人模块的工具栏中，可以自动下载最近3天完成的积分模式和暗黑模式视频。

## 使用方法

### 1. 启动视频下载

1. 打开光流一站式口播助手
2. 切换到"数字人"标签页
3. 在工具栏中找到"视频下载"按钮（位于"音频位置"按钮后面）
4. 点击"视频下载"按钮开始下载

### 2. 下载流程

下载器会自动执行以下步骤：

1. **扫描任务文件夹**：查找最近3天的`创作任务_YYYYMMDD`文件夹
2. **解析记录文件**：读取每个文件夹中的`生成结果记录_YYYYMMDD.xlsx`文件
3. **筛选待下载视频**：找出生成模式为"积分"或"暗黑"且完成状态不是"已交付"的视频
4. **并发下载**：启动多个浏览器实例同时进行下载
5. **分类保存**：根据模式将视频保存到**运行当天**的创作任务文件夹中对应的"积分"或"暗黑"文件夹
6. **更新状态**：将成功下载的视频状态更新为"已交付"

### 3. 配置设置

在设置面板的"数字人设置"分组中，可以配置以下参数：

#### 基础设置
- **启用无头模式**：控制浏览器是否显示界面（默认启用，视频下载会遵循此设置）

#### 视频下载设置
- **并发浏览器数**：同时运行的浏览器实例数量（默认3个，范围1-10）
- **搜索天数**：查找最近几天的任务文件夹（默认3天，范围1-30）
- **启用下载截图**：是否在下载过程中保存截图（默认关闭，仅用于调试）

### 4. 停止下载

如果需要停止正在进行的下载：

1. 再次点击"视频下载"按钮（此时显示为"停止下载"）
2. 在弹出的确认对话框中选择"是"

## 技术特性

### 智能筛选
- 自动识别积分模式和暗黑模式的视频
- 跳过已经标记为"已交付"的视频
- 检测暗黑模式视频的生成状态，跳过正在生成中的视频

### 并发处理
- 支持多浏览器并发下载，提高效率
- 可配置并发数量，适应不同的系统性能
- 遵循设置中的无头模式配置

### 智能匹配
- 使用视频名称中的ID进行精确匹配
- 支持完整名称匹配和模糊匹配
- 防止下载错误的视频

### 状态管理
- 实时更新Excel文件中的完成状态
- 自动添加完成日期
- 保持原有数据结构完整性

### 认证管理
- 自动使用现有的飞影网站认证信息
- 无需重复登录
- 支持认证数据更新

## 文件组织

下载的视频会按以下结构保存到**feiyingshuziren目录下运行当天**的创作任务文件夹：

```
feiyingshuziren/
└── 创作任务_20250725/  (运行当天的文件夹)
    ├── 积分/                      (积分模式视频)
    │   ├── video1.mp4
    │   └── video2.mp4
    └── 暗黑/                      (暗黑模式视频)
        ├── video3.mp4
        └── video4.mp4
```

**重要说明**：
- 视频始终保存到feiyingshuziren目录下运行程序当天的创作任务文件夹中
- 如果当天的文件夹不存在，会自动创建
- 如果对应的"积分"或"暗黑"子文件夹不存在，也会自动创建
- 这样可以确保下载的视频统一管理在feiyingshuziren目录的当天工作目录中

## 注意事项

1. **网络连接**：确保网络连接稳定，下载大文件可能需要较长时间
2. **浏览器依赖**：功能依赖playwright库，如未安装会提示用户
3. **认证信息**：确保`feiyingshuziren/essential_auth_data.json`文件存在且有效
4. **并发限制**：建议根据系统性能调整并发浏览器数量（1-10个）
5. **磁盘空间**：确保有足够的磁盘空间存储下载的视频文件
6. **无头模式**：可以在设置中切换是否显示浏览器界面，便于调试或监控下载过程

## 故障排除

### 常见问题

1. **未找到任务文件夹**
   - 检查是否存在最近3天的`创作任务_YYYYMMDD`文件夹
   - 可以在设置中调整搜索天数（1-30天）

2. **未找到Excel文件**
   - 确保任务文件夹中存在`生成结果记录_YYYYMMDD.xlsx`文件
   - 检查文件名格式是否正确

3. **认证失败**
   - 使用"更新授权"功能重新获取认证信息
   - 检查认证文件是否存在且有效

4. **下载失败**
   - 检查网络连接
   - 确认视频在飞影网站中确实存在
   - 查看日志了解具体错误信息
   - 尝试关闭无头模式查看浏览器具体操作过程

5. **找不到视频文件**
   - 检查feiyingshuziren目录下运行当天的创作任务文件夹
   - 确认视频是否保存在正确的"积分"或"暗黑"子文件夹中

## 日志信息

所有下载过程都会在数字人模块的日志面板中显示详细信息，包括：
- 扫描到的任务文件夹
- 解析到的视频数量
- 下载配置信息（并发数、搜索天数、无头模式等）
- 下载进度和状态
- 文件夹创建信息
- 视频保存路径
- 成功/失败的视频列表
- 详细的错误信息

通过查看日志可以了解下载过程的详细情况和排查问题。