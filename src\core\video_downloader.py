#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
视频下载器模块
基于hifly_video_downloader.py实现的视频批量下载功能
用于下载最近3天完成的积分模式和暗黑模式视频
"""

import os
import sys
import re
import asyncio
import time
import json
import pandas as pd
from datetime import datetime, timedelta
from PySide6.QtCore import QObject, Signal, QThread
import traceback

try:
    from playwright.async_api import async_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    async_playwright = None


class VideoDownloader(QObject):
    """视频下载器类"""
    
    # 信号定义
    log_message = Signal(str)  # 日志消息
    progress_updated = Signal(int, int)  # 进度更新 (当前, 总数)
    download_completed = Signal(str, bool, str)  # 下载完成 (视频名, 成功, 消息)
    all_completed = Signal(dict)  # 全部完成 (结果统计)
    
    def __init__(self, project_root=None):
        super().__init__()
        
        # 项目根目录
        if project_root:
            self.project_root = project_root
        else:
            # 自动检测项目根目录
            current_dir = os.path.dirname(os.path.abspath(__file__))  # core目录
            src_dir = os.path.dirname(current_dir)  # src目录
            self.project_root = os.path.dirname(src_dir)  # 项目根目录
        
        # 认证文件路径
        self.auth_file = os.path.join(self.project_root, "feiyingshuziren", "essential_auth_data.json")
        
        # 默认配置
        self.concurrent_browsers = 3
        self.search_days = 3
        self.enable_screenshots = False
        
        # 停止标志
        self.stop_requested = False
    
    def set_config(self, concurrent_browsers=3, search_days=3, enable_screenshots=False, headless_mode=True):
        """设置下载配置"""
        self.concurrent_browsers = concurrent_browsers
        self.search_days = search_days
        self.enable_screenshots = enable_screenshots
        self.headless_mode = headless_mode
        self.log_message.emit(f"配置已更新: 并发浏览器={concurrent_browsers}, 搜索天数={search_days}, 无头模式={headless_mode}")
    
    def stop_download(self):
        """停止下载"""
        self.stop_requested = True
        self.log_message.emit("正在停止下载任务...")
    
    def find_recent_task_folders(self):
        """查找最近指定天数的创作任务文件夹"""
        task_folders = []
        current_date = datetime.now()
        
        # 在feiyingshuziren目录下查找创作任务文件夹
        feiyingshuziren_path = os.path.join(self.project_root, "feiyingshuziren")
        
        if not os.path.exists(feiyingshuziren_path):
            self.log_message.emit(f"feiyingshuziren目录不存在: {feiyingshuziren_path}")
            return task_folders
        
        # 生成最近N天的日期列表
        for i in range(self.search_days):
            target_date = current_date - timedelta(days=i)
            folder_name = f"创作任务_{target_date.strftime('%Y%m%d')}"
            folder_path = os.path.join(feiyingshuziren_path, folder_name)
            
            if os.path.exists(folder_path):
                task_folders.append(folder_path)
                self.log_message.emit(f"找到任务文件夹: {folder_name}")
        
        if not task_folders:
            self.log_message.emit(f"在feiyingshuziren目录下未找到最近{self.search_days}天的创作任务文件夹")
        
        return task_folders
    
    def find_excel_file(self, folder_path):
        """在指定文件夹中查找生成结果记录.xlsx文件"""
        folder_name = os.path.basename(folder_path)
        
        # 优先查找带日期后缀的文件
        date_suffix = folder_name.replace("创作任务_", "")
        excel_with_date = os.path.join(folder_path, f"生成结果记录_{date_suffix}.xlsx")
        
        if os.path.exists(excel_with_date):
            return excel_with_date
        
        # 查找通用文件名
        excel_generic = os.path.join(folder_path, "生成结果记录.xlsx")
        if os.path.exists(excel_generic):
            return excel_generic
        
        return None
    
    def read_video_records(self, file_path):
        """从Excel文件中读取视频记录，筛选需要下载的视频"""
        try:
            # 读取Excel文件
            df = pd.read_excel(file_path, engine='openpyxl')
            self.log_message.emit(f"成功读取文件: {os.path.basename(file_path)}")
            
            # 检查必需列
            required_columns = ['名称']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                self.log_message.emit(f"文件缺少必需列: {missing_columns}")
                return [], df
            
            # 确保有完成状态列
            if '完成状态' not in df.columns:
                df['完成状态'] = ''
            
            if '生成模式' not in df.columns:
                df['生成模式'] = '积分'  # 默认为积分模式
            
            # 筛选需要下载的视频
            video_info_list = []
            for index, row in df.iterrows():
                name = str(row['名称']).strip()
                mode = str(row.get('生成模式', '积分')).strip()
                status = str(row.get('完成状态', '')).strip()
                
                # 只处理积分模式和暗黑模式，且状态不是已交付的
                if ('积分' in mode or '暗黑' in mode) and status != '已交付':
                    video_mode = '暗黑' if '暗黑' in mode else '积分'
                    video_info_list.append({
                        'name': name,
                        'mode': video_mode,
                        'index': index,
                        'file_path': file_path
                    })
            
            total_count = len(df)
            need_download = len(video_info_list)
            already_delivered = sum(1 for status in df['完成状态'] if str(status).strip() == '已交付')
            
            self.log_message.emit(f"文件统计: 总计{total_count}个, 已交付{already_delivered}个, 需下载{need_download}个")
            
            return video_info_list, df
            
        except Exception as e:
            self.log_message.emit(f"读取文件失败: {str(e)}")
            return [], None
    
    def load_auth_data(self):
        """加载认证数据"""
        if not os.path.exists(self.auth_file):
            self.log_message.emit(f"警告: 认证文件不存在 - {self.auth_file}")
            return {"cookies": [], "localStorage": {}}
        
        try:
            with open(self.auth_file, "r", encoding="utf-8") as f:
                auth_data = json.load(f)
            
            cookies = auth_data.get("cookies", [])
            local_storage = auth_data.get("localStorage", {})
            
            self.log_message.emit(f"已加载认证数据: {len(cookies)}个cookie, {len(local_storage)}个localStorage项")
            return auth_data
            
        except Exception as e:
            self.log_message.emit(f"加载认证数据失败: {str(e)}")
            return {"cookies": [], "localStorage": {}}
    
    def update_download_status(self, df, file_path, video_info, status):
        """更新Excel文件中的完成状态"""
        try:
            # 更新数据帧中的状态
            df.at[video_info['index'], '完成状态'] = status
            
            # 如果状态是已交付，添加完成日期
            if status == '已交付' and '完成日期' in df.columns:
                today = datetime.now()
                date_str = f"{today.month}月{today.day}日"
                df.at[video_info['index'], '完成日期'] = date_str
            
            # 保存到Excel文件
            df.to_excel(file_path, index=False)
            self.log_message.emit(f"已更新 '{video_info['name']}' 状态为: {status}")
            return True
            
        except Exception as e:
            self.log_message.emit(f"更新状态失败 '{video_info['name']}': {str(e)}")
            return False
    
    async def check_login_status(self, page):
        """检查是否已登录"""
        try:
            # 检查登录状态的多种方式
            login_button = await page.query_selector('button:has-text("登录")')
            if login_button:
                return False
            
            # 检查用户相关元素
            user_elements = await page.query_selector('.user-avatar, .user-name, .ant-dropdown-trigger')
            if user_elements:
                return True
            
            # 检查页面内容
            content = await page.content()
            if '登录' in content and '注册' in content:
                return False
            
            return True
            
        except Exception as e:
            self.log_message.emit(f"检查登录状态失败: {str(e)}")
            return False
    
    async def find_and_download_video(self, page, video_info):
        """查找并下载指定视频"""
        try:
            video_name = video_info['name']
            self.log_message.emit(f"开始处理视频: {video_name}")
            
            # 等待页面加载
            await page.wait_for_timeout(2000)
            
            # 提取视频ID（6位数字）
            id_match = re.search(r'\d{6}', video_name)
            if not id_match:
                self.log_message.emit(f"无法从视频名称中提取ID: {video_name}")
                return False
            
            video_id = id_match.group(0)
            self.log_message.emit(f"提取到视频ID: {video_id}")
            
            # 检查是否是暗黑模式视频在生成中
            if video_info['mode'] == '暗黑':
                generating_status = await self.check_video_generating(page, video_id)
                if generating_status:
                    self.log_message.emit(f"暗黑模式视频生成中，跳过: {video_name}")
                    return 'GENERATING'
            
            # 查找匹配的视频卡片
            success = await self.click_download_button(page, video_id, video_name)
            if success:
                # 等待下载触发
                await page.wait_for_timeout(3000)
                return True
            
            return False
            
        except Exception as e:
            self.log_message.emit(f"处理视频失败 '{video_info['name']}': {str(e)}")
            return False
    
    async def check_video_generating(self, page, video_id):
        """检查视频是否在生成中"""
        try:
            # 查找包含指定ID的卡片
            cards = await page.query_selector_all('.list-box')
            for card in cards:
                card_text = await card.text_content()
                if video_id in card_text:
                    # 检查生成中状态
                    has_generating = await page.evaluate("""
                        (cardElement) => {
                            const hasGeneratingText = cardElement.textContent.includes('生成中');
                            const hasLoadingIcon = cardElement.querySelector('.anticon-loading') !== null;
                            const hasPendingClass = cardElement.querySelector('.pending') !== null;
                            return hasGeneratingText || hasLoadingIcon || hasPendingClass;
                        }
                    """, card)
                    
                    return has_generating
            
            return False
            
        except Exception as e:
            self.log_message.emit(f"检查生成状态失败: {str(e)}")
            return False
    
    async def click_download_button(self, page, video_id, video_name):
        """点击下载按钮"""
        try:
            # 方法1: 精确匹配视频名称
            card_selector = f".box-title:text-is('{video_name}')"
            card_title = await page.query_selector(card_selector)
            
            if not card_title:
                # 方法2: 包含ID的模糊匹配
                card_selector = f".box-title:has-text('{video_id}')"
                card_title = await page.query_selector(card_selector)
            
            if card_title:
                # 滚动到卡片位置
                await card_title.scroll_into_view_if_needed()
                await page.wait_for_timeout(1000)
                
                # 悬停激活下载按钮
                await card_title.hover()
                await page.wait_for_timeout(1000)
                
                # 查找并点击下载按钮
                download_btn = await page.query_selector(f"{card_selector} >> xpath=../.. >> .anticon-download")
                if download_btn:
                    await download_btn.click()
                    self.log_message.emit(f"已点击下载按钮: {video_name}")
                    return True
                else:
                    # 尝试点击操作按钮
                    op_btn = await page.query_selector(f"{card_selector} >> xpath=../.. >> .op .btn")
                    if op_btn:
                        await op_btn.click()
                        self.log_message.emit(f"已点击操作按钮: {video_name}")
                        return True
            
            self.log_message.emit(f"未找到匹配的视频卡片: {video_name}")
            return False
            
        except Exception as e:
            self.log_message.emit(f"点击下载按钮失败: {str(e)}")
            return False
    
    async def process_videos_with_browser(self, videos_chunk, browser_id=1):
        """使用单个浏览器实例处理一组视频"""
        if not PLAYWRIGHT_AVAILABLE:
            self.log_message.emit("Playwright未安装，无法执行下载")
            return {"成功": [], "失败": videos_chunk}
        
        results = {"成功": [], "失败": []}
        auth_data = self.load_auth_data()
        
        if not auth_data.get("cookies"):
            self.log_message.emit(f"浏览器#{browser_id}: 无有效认证数据")
            results["失败"].extend(videos_chunk)
            return results
        
        async with async_playwright() as p:
            try:
                # 启动浏览器
                browser = await p.chromium.launch(headless=self.headless_mode)
                context = await browser.new_context(
                    user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    accept_downloads=True
                )
                
                page = await context.new_page()
                page.set_default_timeout(30000)
                
                # 设置下载处理
                downloads = []
                
                async def handle_download(download):
                    downloads.append(download)
                    filename = download.suggested_filename
                    self.log_message.emit(f"浏览器#{browser_id}: 检测到下载 {filename}")
                    
                    # 匹配视频信息
                    matched_video = None
                    id_match = re.search(r'\d{6}', filename)
                    if id_match:
                        file_id = id_match.group(0)
                        for video in videos_chunk:
                            if file_id in video['name']:
                                matched_video = video
                                break
                    
                    if matched_video:
                        # 根据模式选择保存目录 - 保存到feiyingshuziren目录下运行当天的创作任务文件夹
                        today = datetime.now().strftime('%Y%m%d')
                        feiyingshuziren_path = os.path.join(self.project_root, "feiyingshuziren")
                        today_folder = os.path.join(feiyingshuziren_path, f"创作任务_{today}")
                        
                        # 确保今天的任务文件夹存在
                        if not os.path.exists(today_folder):
                            os.makedirs(today_folder)
                            self.log_message.emit(f"浏览器#{browser_id}: 创建今日任务文件夹 {today_folder}")
                        
                        # 根据模式选择子文件夹
                        if matched_video['mode'] == '暗黑':
                            save_dir = os.path.join(today_folder, "暗黑")
                            self.log_message.emit(f"浏览器#{browser_id}: 暗黑模式视频，保存到暗黑目录")
                        else:
                            save_dir = os.path.join(today_folder, "积分")
                            self.log_message.emit(f"浏览器#{browser_id}: 积分模式视频，保存到积分目录")
                        
                        # 确保模式目录存在
                        os.makedirs(save_dir, exist_ok=True)
                        
                        save_path = os.path.join(save_dir, filename)
                        await download.save_as(save_path)
                        
                        self.log_message.emit(f"浏览器#{browser_id}: 已保存 {filename} 到 {save_path}")
                        
                        # 更新状态
                        df = pd.read_excel(matched_video['file_path'], engine='openpyxl')
                        self.update_download_status(df, matched_video['file_path'], matched_video, '已交付')
                        
                        if matched_video not in results["成功"]:
                            results["成功"].append(matched_video)
                        
                        self.download_completed.emit(matched_video['name'], True, "下载成功")
                
                page.on("download", handle_download)
                
                # 导航并登录
                await page.goto("https://hifly.cc/", timeout=30000)
                
                # 应用认证数据
                await context.add_cookies(auth_data.get("cookies", []))
                local_storage = auth_data.get("localStorage", {})
                for key, value in local_storage.items():
                    await page.evaluate(f"localStorage.setItem('{key}', {json.dumps(value)})")
                
                await page.reload()
                await page.wait_for_timeout(3000)
                
                # 检查登录状态
                is_logged_in = await self.check_login_status(page)
                if not is_logged_in:
                    self.log_message.emit(f"浏览器#{browser_id}: 未登录，跳过此浏览器")
                    results["失败"].extend(videos_chunk)
                    return results
                
                # 导航到视频管理页面
                await page.goto("https://hifly.cc/video", timeout=30000)
                await page.wait_for_timeout(5000)
                
                # 处理每个视频
                for i, video_info in enumerate(videos_chunk):
                    if self.stop_requested:
                        self.log_message.emit("下载已被用户停止")
                        break
                    
                    self.progress_updated.emit(i + 1, len(videos_chunk))
                    
                    success = await self.find_and_download_video(page, video_info)
                    
                    if success == 'GENERATING':
                        # 生成中的视频标记为失败
                        df = pd.read_excel(video_info['file_path'], engine='openpyxl')
                        self.update_download_status(df, video_info['file_path'], video_info, '下载失败')
                        results["失败"].append(video_info)
                        self.download_completed.emit(video_info['name'], False, "视频生成中")
                    elif success:
                        # 等待下载完成
                        await page.wait_for_timeout(5000)
                        # 如果没有检测到下载，标记为失败
                        if not any(d for d in downloads if re.search(r'\d{6}', d.suggested_filename) and 
                                 re.search(r'\d{6}', d.suggested_filename).group(0) in video_info['name']):
                            df = pd.read_excel(video_info['file_path'], engine='openpyxl')
                            self.update_download_status(df, video_info['file_path'], video_info, '下载失败')
                            results["失败"].append(video_info)
                            self.download_completed.emit(video_info['name'], False, "未检测到下载")
                    else:
                        # 下载失败
                        df = pd.read_excel(video_info['file_path'], engine='openpyxl')
                        self.update_download_status(df, video_info['file_path'], video_info, '下载失败')
                        results["失败"].append(video_info)
                        self.download_completed.emit(video_info['name'], False, "找不到视频")
                
                await browser.close()
                
            except Exception as e:
                self.log_message.emit(f"浏览器#{browser_id}执行出错: {str(e)}")
                results["失败"].extend([v for v in videos_chunk if v not in results["成功"]])
        
        return results
    
    async def start_download_async(self):
        """异步开始下载流程"""
        try:
            self.stop_requested = False
            self.log_message.emit("开始视频下载任务...")
            
            # 查找任务文件夹
            task_folders = self.find_recent_task_folders()
            if not task_folders:
                self.log_message.emit("未找到任务文件夹，下载结束")
                return
            
            # 收集所有需要下载的视频
            all_videos = []
            for folder_path in task_folders:
                excel_file = self.find_excel_file(folder_path)
                if excel_file:
                    videos, df = self.read_video_records(excel_file)
                    all_videos.extend(videos)
            
            if not all_videos:
                self.log_message.emit("没有需要下载的视频")
                self.all_completed.emit({"成功": 0, "失败": 0, "总计": 0})
                return
            
            self.log_message.emit(f"总共找到 {len(all_videos)} 个需要下载的视频")
            
            # 分配给多个浏览器
            videos_per_browser = max(1, len(all_videos) // self.concurrent_browsers)
            browser_tasks = []
            
            for i in range(self.concurrent_browsers):
                start_idx = i * videos_per_browser
                if i == self.concurrent_browsers - 1:
                    # 最后一个浏览器处理剩余所有视频
                    end_idx = len(all_videos)
                else:
                    end_idx = start_idx + videos_per_browser
                
                if start_idx < len(all_videos):
                    videos_chunk = all_videos[start_idx:end_idx]
                    task = self.process_videos_with_browser(videos_chunk, i + 1)
                    browser_tasks.append(task)
            
            # 并发执行
            self.log_message.emit(f"启动 {len(browser_tasks)} 个浏览器实例进行并发下载")
            all_results = await asyncio.gather(*browser_tasks)
            
            # 合并结果
            total_success = []
            total_failed = []
            
            for result in all_results:
                total_success.extend(result["成功"])
                total_failed.extend(result["失败"])
            
            # 发送完成信号
            final_results = {
                "成功": len(total_success),
                "失败": len(total_failed),
                "总计": len(all_videos),
                "成功列表": [v['name'] for v in total_success],
                "失败列表": [v['name'] for v in total_failed]
            }
            
            self.log_message.emit(f"下载完成! 成功:{len(total_success)}, 失败:{len(total_failed)}")
            self.all_completed.emit(final_results)
            
        except Exception as e:
            self.log_message.emit(f"下载过程出错: {str(e)}")
            self.log_message.emit(traceback.format_exc())
    
    def start_download(self):
        """开始下载（从主线程调用）"""
        if not PLAYWRIGHT_AVAILABLE:
            self.log_message.emit("错误: 未安装playwright，请先安装: pip install playwright")
            return
        
        # 在新的事件循环中运行异步任务
        try:
            asyncio.run(self.start_download_async())
        except Exception as e:
            self.log_message.emit(f"启动下载失败: {str(e)}")


class VideoDownloadWorker(QThread):
    """视频下载工作线程"""
    
    def __init__(self, downloader):
        super().__init__()
        self.downloader = downloader
    
    def run(self):
        """运行下载任务"""
        self.downloader.start_download()