#!/usr/bin/env python3
"""
创建示例的avater_list.xlsx文件
"""

import pandas as pd
import os
from datetime import datetime, timedelta

def create_sample_avatar_list():
    """创建示例的素材列表文件"""
    try:
        # 确保data目录存在
        data_dir = "data"
        os.makedirs(data_dir, exist_ok=True)
        
        # 创建示例数据
        sample_data = []
        
        # 添加一些示例记录
        for i in range(10):
            days_ago = i
            update_date = (datetime.now() - timedelta(days=days_ago)).strftime('%Y-%m-%d %H:%M:%S')
            
            record = {
                'ID': f'37{940 + i}',
                '视频URL': f'https://dv-library.gz.bcebos.com/Files/Uploads/20250722/sample{i}/37{940 + i}-{i+1}.mp4',
                '上传人邮箱后缀': f'user{i}@baidu.com',
                '拍摄演员名称': f'演员{i+1}',
                '视频版型': '横版' if i % 2 == 0 else '竖版',
                '场景': f'场景{i+1}',
                '表现形式': f'表现{i+1}',
                '服装': f'服装{i+1}',
                '是否上传飞影': '是' if i % 3 == 0 else '',
                '更新日期': update_date
            }
            sample_data.append(record)
        
        # 创建DataFrame
        df = pd.DataFrame(sample_data)
        
        # 保存到Excel文件
        file_path = os.path.join(data_dir, "avater_list.xlsx")

        # 检查文件是否已存在
        if os.path.exists(file_path):
            print(f"ℹ️ 文件已存在: {file_path}")
            print("📊 读取现有文件内容...")
            existing_df = pd.read_excel(file_path)
            print(f"📊 现有文件包含 {len(existing_df)} 条记录")
            return True

        df.to_excel(file_path, index=False)
        
        print(f"✅ 示例素材列表文件已创建: {file_path}")
        print(f"📊 包含 {len(sample_data)} 条示例记录")
        print("📋 列信息:")
        for col in df.columns:
            print(f"   - {col}")
        
        # 显示最近一周的数据
        one_week_ago = datetime.now() - timedelta(days=7)
        df['更新日期'] = pd.to_datetime(df['更新日期'])
        recent_data = df[df['更新日期'] >= one_week_ago]
        print(f"\n📅 最近一周数据: {len(recent_data)} 条记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建示例文件失败: {e}")
        return False

if __name__ == "__main__":
    success = create_sample_avatar_list()
    if success:
        print("\n🎉 示例文件创建成功！现在可以测试视频管理功能了。")
    else:
        print("\n❌ 示例文件创建失败")
