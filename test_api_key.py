#!/usr/bin/env python3
"""
测试API密钥读取
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_api_key():
    """测试API密钥读取"""
    try:
        from core.config_manager import ConfigManager
        
        print("✓ 配置管理器导入成功")
        
        # 创建配置管理器实例
        config_manager = ConfigManager()
        print("✓ 配置管理器实例创建成功")
        
        # 读取API密钥
        api_key = config_manager.get("api_key", "")
        print(f"✓ API密钥读取成功: {'已配置' if api_key else '未配置'}")
        
        if api_key:
            print(f"  密钥前缀: {api_key[:8]}...")
        else:
            print("  请在设置中配置Fish Audio API密钥")
        
        # 读取其他相关配置
        api_endpoint = config_manager.get("api_endpoint", "")
        model = config_manager.get("model", "")
        
        print(f"✓ API端点: {api_endpoint}")
        print(f"✓ 模型: {model}")
        
        print("\n🎉 API密钥测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ API密钥测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始测试API密钥读取...\n")
    
    success = test_api_key()
    
    print("\n" + "="*50 + "\n")
    
    if success:
        print("🎉 测试通过！声音管理可以正确读取API密钥。")
    else:
        print("❌ 测试失败，请检查错误信息。")
        sys.exit(1)
