# 声音管理功能使用说明

## 功能概述

声音管理模块是新增的功能，用于管理Fish Audio平台的声音模型。该模块提供了声音模型的获取、存储、查看和同步功能。

## 功能特性

### 1. 音频位置管理
- **功能**: 打开音频文件存放目录
- **用途**: 管理需要上传的音频文件
- **操作**: 点击"音频位置"按钮即可打开文件夹
- **文件夹位置**: `voice_management/audio_files/`

### 2. 声音ID获取（智能增量保存）
- **功能**: 从Fish Audio平台获取用户自己的声音模型ID
- **特点**:
  - 只获取当前API密钥账户下的模型
  - 智能增量保存，避免重复数据
  - 自动读取Excel文件中已存在的声音名称
  - 自动读取JSON文件中已存在的声音名称
  - 基于声音名称进行去重判断
  - 只保存新增的声音模型
  - 自动保存到Excel文件（voice_management/声音ID列表.xlsx）
- **操作**: 点击"获取ID"按钮开始获取
- **前提**: 需要在设置中配置Fish Audio API密钥
- **注意**: 不会获取公开的30000+模型，只获取您自己创建的模型

### 3. 音频上传
- **功能**: 将本地音频文件上传到Fish Audio进行声音克隆
- **特点**:
  - 支持多种音频格式（MP3、WAV、FLAC、M4A、OGG）
  - 自动创建声音模型
  - 上传成功后自动移动文件到已上传文件夹
  - 实时显示上传进度和状态
- **操作**: 点击"开始上传"按钮开始上传
- **前提**: 需要在音频文件夹中放置音频文件

### 4. 数据展示
- **表格列**: 序号、名称、模型ID、网址、提取时间
- **功能**:
  - 支持排序
  - 支持搜索
  - 支持多选
  - 自动刷新

### 5. 百度表格自动同步 ✅
- **功能**: 上传完成后自动将声音模型数据同步到百度线上表格
- **特点**:
  - 上传完成后自动触发同步
  - 只同步4列关键数据：名称、modelId、网址、新增时间
  - 使用百度表格官方API进行同步
  - 支持状态码200和201的成功响应
  - 异步处理，不阻塞界面操作
  - 复用声音克隆模块的百度表格配置
  - 详细的同步日志和错误处理
- **配置**: 使用声音克隆模块中的知识库URL和API密钥
- **操作**: 无需手动操作，上传完成后自动同步
- **状态**: 已完成并测试通过

## 使用流程

### 第一步：配置API密钥
1. 点击右下角的设置按钮
2. 在"声音克隆设置"中输入Fish Audio API密钥
3. 保存设置
**注意**: 声音管理与声音克隆使用相同的API密钥，无需重复配置

### 第二步：访问声音管理
1. 在左侧侧边栏找到"声音管理"按钮（位于数字人按钮下方）
2. 点击进入声音管理页面

### 第三步：上传音频文件
1. 点击"音频位置"按钮打开音频文件夹
2. 将需要上传的音频文件放入该文件夹
3. 点击"开始上传"按钮
4. 确认上传操作
5. 等待上传完成，文件会自动移动到已上传文件夹
6. 查看表格中的新增声音模型
7. 系统自动同步新模型到百度表格（如果已配置）

### 第四步：获取现有声音模型
1. 点击"获取ID"按钮
2. 确认获取操作
3. 等待获取完成
4. 查看表格中的新增数据

## 技术实现

### 核心组件
- **VoiceManager**: 声音管理器核心类
- **Fish Audio API**: 使用官方API获取模型数据
- **数据存储**: JSON格式本地存储
- **UI集成**: 完全集成到现有界面框架

### API端点
- **获取模型列表**: `GET /model`
- **创建模型**: `POST /model`
- **参数**:
  - 获取：page_size, page_number, sort_by
  - 创建：title, type, visibility, train_mode, enhance_audio_quality, voices
- **认证**: Bearer Token

### 数据结构
```json
{
  "name": "模型名称",
  "modelId": "模型ID",
  "url": "模型网址",
  "extractTime": "提取时间",
  "type": "模型类型",
  "description": "描述",
  "author": "作者",
  "state": "状态",
  "visibility": "可见性",
  "created_at": "创建时间",
  "like_count": "点赞数",
  "task_count": "任务数"
}
```

## 文件结构

```
voice_management/
├── 声音ID列表.xlsx            # Excel格式的声音模型数据
├── voice_models.json          # JSON格式的声音模型数据
├── audio_files/              # 待上传音频文件
└── uploaded_files/           # 已上传音频文件
```

## 注意事项

1. **API限制**: 请注意Fish Audio API的调用频率限制
2. **网络连接**: 获取功能需要稳定的网络连接
3. **数据备份**: 建议定期备份voice_models.json文件
4. **文件管理**: 及时清理已上传的音频文件

## 界面说明

### 工具栏
- **搜索框**: 实时搜索声音模型，支持多列搜索，输入2个字符开始过滤
- **清除按钮**: 清除搜索内容，显示所有记录
- **音频位置**: 打开音频文件夹（带文件夹图标）
- **获取ID**: 从Fish Audio获取声音模型ID（带刷新图标）

### 表格区域
- **外边框**: 美观的圆角边框设计
- **列标题**: 序号、名称、模型ID、网址、提取时间
- **排序功能**: 点击列标题可排序
- **选择功能**: 支持单选和多选

### 底部控制区
- **开始上传**: 上传音频文件到Fish Audio
- **进度条**: 显示上传进度和状态（200px宽，24px高）
- **进度信息**: 显示当前操作状态

### 日志区域
- **边框样式**: 与其他模块一致的边框设计
- **展开/收起**: 可控制日志区域显示
- **等宽字体**: 便于阅读日志信息
- **自动滚动**: 新日志自动滚动到底部

## 故障排除

### 常见问题

1. **获取失败**
   - 检查API密钥是否正确
   - 检查网络连接
   - 查看日志区域的错误信息

2. **文件夹打开失败**
   - 检查文件夹权限
   - 确认目录是否存在

3. **数据不显示**
   - 刷新页面
   - 检查数据文件是否损坏

### 日志查看
- 所有操作都会在日志区域显示详细信息
- 可以通过展开/收起按钮控制日志显示

## 后续开发计划

1. **百度表格同步**: 实现与百度在线表格的数据同步
2. **数据导出**: 支持导出为Excel格式
3. **高级搜索**: 支持多条件筛选
4. **批量操作**: 支持批量删除、导出等操作
5. **数据统计**: 添加数据统计和分析功能
