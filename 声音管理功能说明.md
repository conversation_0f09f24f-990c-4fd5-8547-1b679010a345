# 声音管理功能使用说明

## 功能概述

声音管理模块是新增的功能，用于管理Fish Audio平台的声音模型。该模块提供了声音模型的获取、存储、查看和同步功能。

## 功能特性

### 1. 音频位置管理
- **功能**: 打开音频文件存放目录
- **用途**: 管理需要上传的音频文件
- **操作**: 点击"音频位置"按钮即可打开文件夹
- **文件夹位置**: `voice_management/audio_files/`

### 2. 声音ID获取
- **功能**: 从Fish Audio平台增量获取声音模型ID
- **特点**: 
  - 增量获取，避免重复数据
  - 支持分页获取，处理大量数据
  - 自动去重，确保数据唯一性
- **操作**: 点击"获取ID"按钮开始获取
- **前提**: 需要在设置中配置Fish Audio API密钥

### 3. 数据展示
- **表格列**: 序号、名称、模型ID、网址、提取时间、类型、作者、状态、可见性、点赞数
- **功能**: 
  - 支持排序
  - 支持搜索
  - 支持多选
  - 自动刷新

### 4. ID同步（待实现）
- **功能**: 将声音ID数据同步到百度线上表格
- **用途**: 方便团队协作和数据共享
- **状态**: 功能框架已完成，具体实现待后续开发

## 使用流程

### 第一步：配置API密钥
1. 点击右下角的设置按钮
2. 在"声音克隆设置"中输入Fish Audio API密钥
3. 保存设置

### 第二步：访问声音管理
1. 在左侧侧边栏找到"声音管理"按钮（位于数字人按钮下方）
2. 点击进入声音管理页面

### 第三步：管理音频文件
1. 点击"音频位置"按钮打开音频文件夹
2. 将需要上传的音频文件放入该文件夹
3. 上传完成后，文件会自动移动到已上传文件夹

### 第四步：获取声音模型
1. 点击"获取ID"按钮
2. 确认获取操作
3. 等待获取完成
4. 查看表格中的新增数据

## 技术实现

### 核心组件
- **VoiceManager**: 声音管理器核心类
- **Fish Audio API**: 使用官方API获取模型数据
- **数据存储**: JSON格式本地存储
- **UI集成**: 完全集成到现有界面框架

### API端点
- **获取模型列表**: `GET /model`
- **参数**: page_size, page_number, sort_by
- **认证**: Bearer Token

### 数据结构
```json
{
  "name": "模型名称",
  "modelId": "模型ID",
  "url": "模型网址",
  "extractTime": "提取时间",
  "type": "模型类型",
  "description": "描述",
  "author": "作者",
  "state": "状态",
  "visibility": "可见性",
  "created_at": "创建时间",
  "like_count": "点赞数",
  "task_count": "任务数"
}
```

## 文件结构

```
voice_management/
├── voice_models.json          # 声音模型数据
├── audio_files/              # 待上传音频文件
└── uploaded_files/           # 已上传音频文件
```

## 注意事项

1. **API限制**: 请注意Fish Audio API的调用频率限制
2. **网络连接**: 获取功能需要稳定的网络连接
3. **数据备份**: 建议定期备份voice_models.json文件
4. **文件管理**: 及时清理已上传的音频文件

## 故障排除

### 常见问题

1. **获取失败**
   - 检查API密钥是否正确
   - 检查网络连接
   - 查看日志区域的错误信息

2. **文件夹打开失败**
   - 检查文件夹权限
   - 确认目录是否存在

3. **数据不显示**
   - 刷新页面
   - 检查数据文件是否损坏

### 日志查看
- 所有操作都会在日志区域显示详细信息
- 可以通过展开/收起按钮控制日志显示

## 后续开发计划

1. **百度表格同步**: 实现与百度在线表格的数据同步
2. **数据导出**: 支持导出为Excel格式
3. **高级搜索**: 支持多条件筛选
4. **批量操作**: 支持批量删除、导出等操作
5. **数据统计**: 添加数据统计和分析功能
