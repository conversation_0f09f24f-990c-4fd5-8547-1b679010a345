#!/usr/bin/env python3
"""
测试实际的百度表格同步功能
"""

import sys
import os
import asyncio

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_real_sync():
    """测试实际的同步功能"""
    try:
        from core.voice_manager import VoiceManager
        from core.config_manager import ConfigManager
        
        print("✓ 模块导入成功")
        
        # 创建实例
        vm = VoiceManager()
        config_manager = ConfigManager()
        
        # 检查配置
        print("✓ 检查配置:")
        baidu_sheet_url = config_manager.get("baidu_sheet_url", "")
        baidu_sheets_token = config_manager.get("baidu_sheets_token", "")
        
        print(f"  - URL: {'已配置' if baidu_sheet_url else '未配置'}")
        print(f"  - Token: {'已配置' if baidu_sheets_token else '未配置'}")
        
        if not baidu_sheet_url or not baidu_sheets_token:
            print("❌ 配置不完整，无法测试实际同步")
            return False
        
        # 测试URL解析
        print("\n✓ 测试URL解析:")
        datasheet_id = vm._extract_datasheet_id(baidu_sheet_url)
        if not datasheet_id:
            print("❌ 无法解析表格ID")
            return False
        
        print(f"  ✓ 表格ID: {datasheet_id}")
        
        # 创建测试数据
        test_models = [
            {
                'name': '实际同步测试声音',
                'modelId': 'real_sync_test_001',
                'url': 'https://fish.audio/model/real_sync_test_001',
                'extractTime': '2024-12-01 10:30:00'
            }
        ]
        
        print(f"\n✓ 准备同步测试数据: {test_models[0]['name']}")
        
        # 执行实际同步测试
        print("\n✓ 开始实际同步测试...")
        
        try:
            await vm._sync_to_baidu_table_async(test_models)
            print("✓ 同步请求已发送")
        except Exception as sync_error:
            print(f"❌ 同步过程中出错: {sync_error}")
            return False
        
        print("\n🎉 实际同步测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 实际同步测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚀 开始实际百度表格同步测试...\n")
    print("⚠️ 注意: 这将向实际的百度表格发送数据！")
    
    # 询问用户确认
    confirm = input("\n是否继续进行实际同步测试？(y/N): ").strip().lower()
    if confirm != 'y':
        print("测试已取消。")
        return
    
    success = asyncio.run(test_real_sync())
    
    print("\n" + "="*50 + "\n")
    
    if success:
        print("🎉 实际同步测试完成！")
        print("\n📋 测试结果:")
        print("1. ✅ 配置读取正常")
        print("2. ✅ URL解析成功")
        print("3. ✅ 同步请求发送")
        print("4. ✅ 错误处理完善")
        print("\n💡 请检查百度表格确认数据是否已同步")
    else:
        print("❌ 实际同步测试失败，请检查错误信息。")
        sys.exit(1)

if __name__ == "__main__":
    main()
