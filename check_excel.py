#!/usr/bin/env python3
"""
检查Excel文件内容
"""

import pandas as pd
import os

def check_excel():
    """检查Excel文件内容"""
    excel_file = "声音ID列表.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return
    
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        
        print(f"✓ Excel文件读取成功: {excel_file}")
        print(f"✓ 数据行数: {len(df)}")
        print(f"✓ 列名: {list(df.columns)}")
        print("\n📋 文件内容:")
        print(df.to_string(index=False))
        
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")

if __name__ == "__main__":
    check_excel()
