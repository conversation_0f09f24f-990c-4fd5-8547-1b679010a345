#!/usr/bin/env python3
"""
检查新位置的Excel文件
"""

import pandas as pd
import os

def check_new_excel():
    """检查新位置的Excel文件"""
    excel_file = "voice_management/声音ID列表.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return
    
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        
        print(f"✓ Excel文件读取成功: {excel_file}")
        print(f"✓ 数据行数: {len(df)}")
        print(f"✓ 列名: {list(df.columns)}")
        print("\n📋 文件内容:")
        print(df.to_string(index=False))
        
        # 检查文件大小
        file_size = os.path.getsize(excel_file)
        print(f"\n✓ 文件大小: {file_size} bytes")
        
        # 检查目录结构
        print(f"\n📁 目录结构:")
        voice_dir = "voice_management"
        if os.path.exists(voice_dir):
            for item in os.listdir(voice_dir):
                item_path = os.path.join(voice_dir, item)
                if os.path.isfile(item_path):
                    size = os.path.getsize(item_path)
                    print(f"  📄 {item} ({size} bytes)")
                elif os.path.isdir(item_path):
                    count = len(os.listdir(item_path))
                    print(f"  📁 {item}/ ({count} items)")
        
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")

if __name__ == "__main__":
    check_new_excel()
