#!/usr/bin/env python3
"""
重命名avatar文件
"""

import os
import shutil

def rename_avatar_file():
    """重命名avatar文件"""
    try:
        old_path = "data/avater_list.xlsx"
        new_path = "data/avatar_list.xlsx"
        
        print("🔄 开始重命名avatar文件...")
        
        # 检查旧文件是否存在
        if os.path.exists(old_path):
            print(f"✓ 找到旧文件: {old_path}")
            
            # 检查新文件是否已存在
            if os.path.exists(new_path):
                print(f"⚠️ 新文件已存在: {new_path}")
                print("创建备份...")
                backup_path = "data/avatar_list_backup.xlsx"
                shutil.copy2(new_path, backup_path)
                print(f"✓ 备份已创建: {backup_path}")
            
            # 重命名文件
            os.rename(old_path, new_path)
            print(f"✅ 文件重命名成功: {old_path} → {new_path}")
            
            # 验证新文件
            if os.path.exists(new_path):
                file_size = os.path.getsize(new_path)
                print(f"✓ 新文件验证成功，大小: {file_size} 字节")
                return True
            else:
                print("❌ 新文件验证失败")
                return False
                
        else:
            print(f"❌ 旧文件不存在: {old_path}")
            
            # 检查新文件是否已存在
            if os.path.exists(new_path):
                print(f"✓ 正确的文件已存在: {new_path}")
                return True
            else:
                print("❌ 两个文件都不存在")
                return False
                
    except Exception as e:
        print(f"❌ 重命名失败: {e}")
        return False

def main():
    success = rename_avatar_file()
    
    if success:
        print("\n🎉 文件重命名完成！")
        print("现在视频管理模块可以正确访问avatar_list.xlsx文件了。")
    else:
        print("\n❌ 文件重命名失败")

if __name__ == "__main__":
    main()
