#!/usr/bin/env python3
"""
测试百度表格URL解析功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_url_parsing():
    """测试URL解析功能"""
    try:
        from core.voice_manager import VoiceManager
        from core.config_manager import ConfigManager
        
        print("✓ 模块导入成功")
        
        # 创建实例
        vm = VoiceManager()
        config_manager = ConfigManager()
        
        # 获取当前配置的URL
        current_url = config_manager.get("baidu_sheet_url", "")
        print(f"✓ 当前配置的URL: {current_url}")
        
        if not current_url:
            print("❌ 未配置百度表格URL，无法测试")
            return False
        
        # 测试URL解析
        print("\n✓ 开始测试URL解析:")
        datasheet_id = vm._extract_datasheet_id(current_url)
        
        if datasheet_id:
            print(f"✓ 成功提取表格ID: {datasheet_id}")
            
            # 构建API URL
            api_url = f"https://ku.baidu-int.com/fusion/v1/datasheets/{datasheet_id}/records"
            print(f"✓ 构建的API URL: {api_url}")
            
            # 检查API Token
            api_token = config_manager.get("baidu_sheets_token", "")
            if api_token:
                print(f"✓ API Token: {api_token[:8]}...")
                print("✓ 配置完整，可以进行API调用")
            else:
                print("❌ API Token未配置")
                return False
            
        else:
            print("❌ 无法提取表格ID")
            return False
        
        # 测试不同格式的URL
        print("\n✓ 测试不同格式的URL:")
        
        test_urls = [
            "https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/83xaWf3nFB/yVphoVLNgomjsf?tb=dstzKKQrWyC6wLK0RZ_viwzvycL6E8P6&type=dst",
            "https://ku.baidu-int.com/knowledge/test?tb=dstABC123_viwDEF456",
            "https://ku.baidu-int.com/knowledge/test?tb=dstXYZ789",
        ]
        
        for i, test_url in enumerate(test_urls, 1):
            print(f"\n  测试URL {i}: {test_url}")
            test_id = vm._extract_datasheet_id(test_url)
            if test_id:
                print(f"    ✓ 提取到ID: {test_id}")
            else:
                print(f"    ❌ 无法提取ID")
        
        print("\n🎉 URL解析测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ URL解析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚀 开始测试百度表格URL解析功能...\n")
    
    success = test_url_parsing()
    
    print("\n" + "="*50 + "\n")
    
    if success:
        print("🎉 测试通过！URL解析功能正常。")
        print("\n📋 解析逻辑:")
        print("1. ✅ 解析URL查询参数")
        print("2. ✅ 提取tb参数值")
        print("3. ✅ 处理dst前缀的表格ID")
        print("4. ✅ 支持带viewId的格式")
        print("5. ✅ 构建正确的API URL")
        print("\n💡 支持的URL格式:")
        print("- tb=dstzKKQrWyC6wLK0RZ_viwzvycL6E8P6")
        print("- tb=dstABC123_viwDEF456")
        print("- tb=dstXYZ789")
    else:
        print("❌ 测试失败，请检查错误信息。")
        sys.exit(1)

if __name__ == "__main__":
    main()
